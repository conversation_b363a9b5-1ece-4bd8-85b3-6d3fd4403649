{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowDemo.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Play, RefreshCw, CheckCircle, XCircle, AlertCircle, Loader2, Save } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TemuWorkflowDemo = () => {\n  _s();\n  const [currentStep, setCurrentStep] = useState(0);\n  const [isRunning, setIsRunning] = useState(false);\n  const [workflowStatus, setWorkflowStatus] = useState('idle'); // idle, running, completed, failed\n  const [visibleSteps, setVisibleSteps] = useState(0); // 控制显示的步骤数量\n  const [failedStep, setFailedStep] = useState(null); // 记录失败的步骤\n  const [showToast, setShowToast] = useState(false); // 控制toast显示\n  const [productProcessingStep, setProductProcessingStep] = useState(0); // 商品加权进度步骤\n  const [startTime, setStartTime] = useState(null); // 执行开始时间\n  const [elapsedTime, setElapsedTime] = useState(0); // 累计执行时间（秒）\n  const intervalRef = useRef(null); // 定时器引用\n  const [isMultiStore, setIsMultiStore] = useState(false); // 是否为多店铺模式\n\n  // 工作流步骤定义\n  const workflowSteps = [{\n    id: 'login',\n    name: '登录',\n    runningDescription: '正在验证登录信息...',\n    completedDescription: '已完成登录【Temu账号】名下的店铺1',\n    duration: 2000,\n    status: 'pending'\n  }, {\n    id: 'product_processing',\n    name: '商品流量加速',\n    runningDescription: '正在为商品流量加速...',\n    completedDescription: '全部商品流量加速任务已完成！所有商品已按规则完成流量加速。',\n    duration: 4000,\n    status: 'pending',\n    progressSteps: ['商品流量加速任务已启动：正在按照预设规则对所有商品进行处理，单个商品处理时间约20-30秒', '商品SPU ID: 204845849 已开启30天普通流量加速加权。开始处理下一个商品...']\n  }, {\n    id: 'result',\n    name: '结果',\n    runningDescription: '正在生成执行结果...',\n    completedDescription: '本次任务执行完毕。共成功处理48个商品。另有2个商品未能成功，原因如下：【商品名称A】因AI判断其价格为6.2，高于5的设定而被跳过；【商品名称B】因页面长时间无法打开而执行失败。',\n    duration: 1000,\n    status: 'pending'\n  }];\n\n  // 多店铺工作流步骤定义\n  const multiStoreWorkflowSteps = [{\n    id: 'login_store1',\n    name: '登录店铺1',\n    runningDescription: '正在验证店铺1登录信息...',\n    completedDescription: '已完成登录【Temu账号】名下的店铺1',\n    duration: 2000,\n    status: 'pending'\n  }, {\n    id: 'traffic_acceleration_store1',\n    name: '流量加速店铺1',\n    runningDescription: '正在为店铺1设置流量加速...',\n    completedDescription: '已完成店铺1的流量加速设置',\n    duration: 3000,\n    status: 'pending'\n  }, {\n    id: 'login_store2',\n    name: '登录店铺2',\n    runningDescription: '正在验证店铺2登录信息...',\n    completedDescription: '已完成登录【Temu账号】名下的店铺2',\n    duration: 2000,\n    status: 'pending'\n  }, {\n    id: 'product_processing_store2',\n    name: '商品加权店铺2',\n    runningDescription: '正在为店铺2商品设置流量加权...',\n    completedDescription: '已完成店铺2商品的流量加权设置',\n    duration: 4000,\n    status: 'pending',\n    progressSteps: ['正在处理店铺2商品', '店铺2商品已开启30天的普通流量加速加权', '已完成店铺2商品流量加权设置']\n  }, {\n    id: 'multi_store_result',\n    name: '汇总结果',\n    runningDescription: '正在生成多店铺执行结果...',\n    completedDescription: '多店铺任务执行完毕',\n    duration: 1000,\n    status: 'pending'\n  }];\n  const [steps, setSteps] = useState(workflowSteps);\n\n  // 时间统计效果\n  useEffect(() => {\n    if (workflowStatus === 'running' && startTime) {\n      intervalRef.current = setInterval(() => {\n        const now = Date.now();\n        const elapsed = Math.floor((now - startTime) / 1000) + elapsedTime;\n        setElapsedTime(elapsed);\n      }, 1000);\n    } else {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n        intervalRef.current = null;\n      }\n    }\n    return () => {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n    };\n  }, [workflowStatus, startTime]);\n\n  // 格式化时间显示\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // 获取步骤描述\n  const getStepDescription = step => {\n    if (step.status === 'running') {\n      // 商品加权步骤显示动态进度文案（支持单店铺和多店铺）\n      if ((step.id === 'product_processing' || step.id === 'product_processing_store2') && step.progressSteps) {\n        return step.progressSteps[productProcessingStep] || step.runningDescription;\n      }\n      return step.runningDescription;\n    } else if (step.status === 'completed') {\n      return step.completedDescription;\n    } else if (step.status === 'failed') {\n      return `执行失败：${step.runningDescription}`;\n    }\n    return '等待执行...';\n  };\n\n  // 模拟工作流执行\n  const executeWorkflow = async () => {\n    setIsRunning(true);\n    setWorkflowStatus('running');\n    setFailedStep(null);\n\n    // 启动计时器（如果是重试，保持累计时间）\n    const now = Date.now();\n    setStartTime(now);\n\n    // 从当前失败步骤或第一步开始\n    const startStep = failedStep !== null ? failedStep : 0;\n    setCurrentStep(startStep);\n\n    // 如果是重新开始，重置步骤状态\n    if (failedStep === null) {\n      setVisibleSteps(0);\n      setSteps(prev => prev.map(step => ({\n        ...step,\n        status: 'pending'\n      })));\n    }\n    for (let i = startStep; i < steps.length; i++) {\n      setCurrentStep(i);\n\n      // 显示当前步骤（如果还未显示）\n      if (i + 1 > visibleSteps) {\n        setVisibleSteps(i + 1);\n        // 确保新显示的步骤处于pending状态\n        setSteps(prev => prev.map((step, index) => index === i ? {\n          ...step,\n          status: 'pending'\n        } : step));\n        await new Promise(resolve => setTimeout(resolve, 300)); // 短暂延迟显示步骤\n      }\n\n      // 设置当前步骤为运行中\n      setSteps(prev => prev.map((step, index) => index === i ? {\n        ...step,\n        status: 'running'\n      } : step));\n\n      // 商品加权步骤的动态文案切换（支持单店铺和多店铺）\n      if ((steps[i].id === 'product_processing' || steps[i].id === 'product_processing_store2') && steps[i].progressSteps) {\n        const progressSteps = steps[i].progressSteps;\n        for (let j = 0; j < progressSteps.length; j++) {\n          setProductProcessingStep(j);\n          await new Promise(resolve => setTimeout(resolve, steps[i].duration / progressSteps.length));\n        }\n      } else {\n        // 等待步骤完成\n        await new Promise(resolve => setTimeout(resolve, steps[i].duration));\n      }\n\n      // 随机失败演示（15%概率，在第2或第3步）\n      const shouldFail = Math.random() < 0.15 && (i === 1 || i === 2);\n      if (shouldFail) {\n        setSteps(prev => prev.map((step, index) => index === i ? {\n          ...step,\n          status: 'failed'\n        } : step));\n        setWorkflowStatus('failed');\n        setFailedStep(i);\n        setIsRunning(false);\n\n        // 失败时保存当前累计时间\n        if (startTime) {\n          const now = Date.now();\n          const currentElapsed = Math.floor((now - startTime) / 1000);\n          setElapsedTime(prev => prev + currentElapsed);\n          setStartTime(null);\n        }\n        return;\n      }\n\n      // 设置步骤为完成\n      setSteps(prev => prev.map((step, index) => index === i ? {\n        ...step,\n        status: 'completed'\n      } : step));\n\n      // 完成一步后显示下一步（如果不是最后一步）\n      if (i < steps.length - 1) {\n        setVisibleSteps(i + 2);\n        // 确保下一步显示为pending状态\n        setSteps(prev => prev.map((step, index) => index === i + 1 ? {\n          ...step,\n          status: 'pending'\n        } : step));\n        await new Promise(resolve => setTimeout(resolve, 500)); // 延迟显示下一步\n      }\n    }\n    setWorkflowStatus('completed');\n    setFailedStep(null);\n    setIsRunning(false);\n\n    // 完成时保存最终累计时间\n    if (startTime) {\n      const now = Date.now();\n      const currentElapsed = Math.floor((now - startTime) / 1000);\n      setElapsedTime(prev => prev + currentElapsed);\n      setStartTime(null);\n    }\n  };\n\n  // 重试失败的步骤\n  const retryFromFailed = () => {\n    if (failedStep !== null) {\n      // 重置失败步骤的状态\n      setSteps(prev => prev.map((step, index) => index === failedStep ? {\n        ...step,\n        status: 'pending'\n      } : step));\n      executeWorkflow();\n    }\n  };\n\n  // 重置整个工作流\n  const resetWorkflow = () => {\n    setIsRunning(false);\n    setWorkflowStatus('idle');\n    setCurrentStep(0);\n    setVisibleSteps(0);\n    setFailedStep(null);\n    setProductProcessingStep(0);\n    setStartTime(null);\n    setElapsedTime(0);\n    const currentWorkflowSteps = isMultiStore ? multiStoreWorkflowSteps : workflowSteps;\n    setSteps(currentWorkflowSteps.map(step => ({\n      ...step,\n      status: 'pending'\n    })));\n  };\n\n  // 保存计划\n  const savePlan = () => {\n    setShowToast(true);\n    setTimeout(() => setShowToast(false), 3000);\n  };\n\n  // 切换到多店铺模式\n  const switchToMultiStore = () => {\n    setIsMultiStore(true);\n    setSteps(multiStoreWorkflowSteps.map(step => ({\n      ...step,\n      status: 'pending'\n    })));\n    setWorkflowStatus('idle');\n    setVisibleSteps(0);\n    setFailedStep(null);\n    setProductProcessingStep(0);\n    setStartTime(null);\n    setElapsedTime(0);\n  };\n\n  // 切换到单店铺模式\n  const switchToSingleStore = () => {\n    setIsMultiStore(false);\n    setSteps(workflowSteps.map(step => ({\n      ...step,\n      status: 'pending'\n    })));\n    setWorkflowStatus('idle');\n    setVisibleSteps(0);\n    setFailedStep(null);\n    setProductProcessingStep(0);\n    setStartTime(null);\n    setElapsedTime(0);\n  };\n\n  // 模拟失败状态\n  const simulateFailure = () => {\n    setVisibleSteps(3);\n    setSteps(prev => prev.map((step, index) => {\n      if (index === 0) return {\n        ...step,\n        status: 'completed'\n      };\n      if (index === 1) return {\n        ...step,\n        status: 'completed'\n      };\n      if (index === 2) return {\n        ...step,\n        status: 'failed'\n      };\n      return step;\n    }));\n    setWorkflowStatus('failed');\n    setFailedStep(2);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg overflow-hidden border border-white/20\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-blue-500 text-white p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-bold\",\n              children: \"AI\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold\",\n              children: \"Temu\\u81EA\\u52A8\\u5316\\u52A9\\u624B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-100 text-sm\",\n              children: \"\\u6D41\\u91CF\\u52A0\\u901F\\u81EA\\u52A8\\u5316\\u6267\\u884C\\u4E2D...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-100 rounded-lg p-4 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-800 mb-2\",\n              children: \"\\u597D\\u7684\\uFF0C\\u5F00\\u59CB\\u6267\\u884C\\u8BA1\\u5212\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600 bg-white rounded-lg p-3 border\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: \"\\u6267\\u884C\\u914D\\u7F6E\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 17\n              }, this), \"\\u666E\\u901A\\u6D41\\u91CF\\u52A0\\u6743\\u6863\\u4F4D\\uFF0C\\u4EF7\\u683C\\u8303\\u56F44-6\\u7F8E\\u5143\\uFF0C\\u65F6\\u654830\\u5929\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this), (workflowStatus === 'running' || workflowStatus === 'completed') && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center\",\n                children: workflowStatus === 'running' ? /*#__PURE__*/_jsxDEV(Loader2, {\n                  className: \"w-3 h-3 text-white animate-spin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: \"w-3 h-3 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-blue-800 font-medium\",\n                children: \"\\u597D\\u7684\\uFF0C\\u5F00\\u59CB\\u6267\\u884C\\u8BA1\\u5212\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-blue-600\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: \"\\u6267\\u884C\\u65F6\\u95F4\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-mono\",\n                children: formatTime(elapsedTime)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: steps.slice(0, visibleSteps).map((step, index) => {\n            const nextStep = steps[index + 1];\n            const shouldShowConnector = index < visibleSteps - 1 && index < steps.length - 1;\n            const connectorColor = step.status === 'completed' ? 'bg-green-500' : 'bg-gray-300';\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [shouldShowConnector && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `absolute left-4 top-8 w-0.5 transition-all duration-500 z-0 ${connectorColor}`,\n                style: {\n                  height: '3rem' // 固定高度，只连接到下一个步骤的起始位置\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-4 relative z-10 pb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0 relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-white rounded-full absolute inset-0 z-10 border border-gray-100\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 388,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 relative z-20 shadow-sm ${step.status === 'completed' ? 'bg-green-500 text-white' : step.status === 'failed' ? 'bg-red-500 text-white' : step.status === 'running' ? 'bg-blue-500 text-white' : step.status === 'pending' ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600'}`,\n                    children: [step.status === 'completed' && /*#__PURE__*/_jsxDEV(CheckCircle, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 398,\n                      columnNumber: 57\n                    }, this), step.status === 'failed' && /*#__PURE__*/_jsxDEV(XCircle, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 399,\n                      columnNumber: 54\n                    }, this), step.status === 'running' && /*#__PURE__*/_jsxDEV(Loader2, {\n                      className: \"w-5 h-5 animate-spin\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 400,\n                      columnNumber: 55\n                    }, this), step.status === 'pending' && /*#__PURE__*/_jsxDEV(Loader2, {\n                      className: \"w-5 h-5 animate-spin\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 401,\n                      columnNumber: 55\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0 pt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-lg font-medium text-gray-900\",\n                      children: step.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 408,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 21\n                  }, this), step.status !== 'failed' && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-600 leading-relaxed\",\n                    children: (step.id === 'result' || step.id === 'multi_store_result') && step.status === 'completed' ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: step.id === 'multi_store_result' ?\n                      /*#__PURE__*/\n                      // 多店铺汇总结果\n                      _jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"font-medium mb-2\",\n                          children: \"\\u672C\\u6B21\\u4EFB\\u52A1\\u6267\\u884C\\u5B8C\\u6BD5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 419,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-2\",\n                          children: \"\\uD83D\\uDCCA \\u5904\\u7406\\u6C47\\u603B\\uFF1A\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 420,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-2\",\n                          children: [\"- \\u5E97\\u94FA1\\uFF1A\\u6210\\u529F\\u5904\\u7406 \", /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"font-semibold text-green-600\",\n                            children: \"48\\u4E2A\\u5546\\u54C1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 421,\n                            columnNumber: 66\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 421,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-2\",\n                          children: [\"- \\u5E97\\u94FA2\\uFF1A\\u6210\\u529F\\u5904\\u7406 \", /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"font-semibold text-green-600\",\n                            children: \"30\\u4E2A\\u5546\\u54C1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 422,\n                            columnNumber: 66\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 422,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-2\",\n                          children: [\"- \\u603B\\u8BA1\\uFF1A\", /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"font-semibold text-blue-600\",\n                            children: \"78\\u4E2A\\u5546\\u54C1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 423,\n                            columnNumber: 60\n                          }, this), \"\\u5904\\u7406\\u5B8C\\u6210\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 423,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-2\",\n                          children: \"\\u26A0\\uFE0F \\u5F02\\u5E38\\u5546\\u54C1\\u8BE6\\u60C5\\uFF1A\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 425,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-2\",\n                          children: \"\\u3010\\u5E97\\u94FA1\\u3011\\u5F02\\u5E38\\u7EDF\\u8BA1\\uFF1A\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 427,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-1\",\n                          children: \"\\u251C\\u2500 \\u4E0D\\u7B26\\u5408\\u6761\\u4EF6(2\\u4E2A)\\uFF1A\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 428,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"space-y-1 pl-4 border-l-2 border-gray-200 mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            children: \"\\u2022 SPU ID: 204845842\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 430,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: \"\\u2022 SPU ID: 204845843\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 431,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 429,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-1\",\n                          children: \"\\u2514\\u2500 \\u5904\\u7406\\u5931\\u8D25(2\\u4E2A)\\uFF1A\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 433,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"space-y-1 pl-4 border-l-2 border-gray-200 mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            children: \"\\u2022 SPU ID: 204845849\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 435,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: \"\\u2022 SPU ID: 204845844\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 436,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 434,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-2\",\n                          children: \"\\u3010\\u5E97\\u94FA2\\u3011\\u5F02\\u5E38\\u7EDF\\u8BA1\\uFF1A\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 439,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-1\",\n                          children: \"\\u251C\\u2500 \\u4E0D\\u7B26\\u5408\\u6761\\u4EF6(2\\u4E2A)\\uFF1A\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 440,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"space-y-1 pl-4 border-l-2 border-gray-200 mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            children: \"\\u2022 SPU ID: 204845842\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 442,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: \"\\u2022 SPU ID: 204845843\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 443,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 441,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-1\",\n                          children: \"\\u2514\\u2500 \\u5904\\u7406\\u5931\\u8D25(2\\u4E2A)\\uFF1A\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 445,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"space-y-1 pl-4 border-l-2 border-gray-200\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            children: \"\\u2022 SPU ID: 204845849\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 447,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: \"\\u2022 SPU ID: 204845844\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 448,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 446,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 418,\n                        columnNumber: 31\n                      }, this) :\n                      /*#__PURE__*/\n                      // 单店铺结果\n                      _jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"font-medium mb-2\",\n                          children: \"\\u672C\\u6B21\\u4EFB\\u52A1\\u6267\\u884C\\u5B8C\\u6BD5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 454,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-2\",\n                          children: [\"\\u5171\\u6210\\u529F\\u5904\\u7406 \", /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"font-semibold text-green-600\",\n                            children: \"48\\u4E2A\\u5546\\u54C1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 455,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 455,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-2\",\n                          children: [\"\\u53E6\\u6709 \", /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"font-semibold text-orange-600\",\n                            children: \"2\\u4E2A\\u5546\\u54C1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 456,\n                            columnNumber: 58\n                          }, this), \" \\u672A\\u80FD\\u6210\\u529F\\uFF0C\\u539F\\u56E0\\u5982\\u4E0B\\uFF1A\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 456,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"space-y-1 pl-4 border-l-2 border-gray-200\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"font-medium\",\n                              children: \"\\u5546\\u54C1\\u540D\\u79F0A\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 458,\n                              columnNumber: 42\n                            }, this), \"\\uFF1A\\u56E0AI\\u5224\\u65AD\\u5176\\u4EF7\\u683C\\u4E3A6.2\\u5143\\uFF0C\\u9AD8\\u4E8E5\\u5143\\u8BBE\\u5B9A\\u800C\\u88AB\\u8DF3\\u8FC7\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 458,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"font-medium\",\n                              children: \"\\u5546\\u54C1\\u540D\\u79F0B\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 459,\n                              columnNumber: 42\n                            }, this), \"\\uFF1A\\u56E0\\u9875\\u9762\\u957F\\u65F6\\u95F4\\u65E0\\u6CD5\\u6253\\u5F00\\u800C\\u6267\\u884C\\u5931\\u8D25\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 459,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 457,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 453,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 415,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: getStepDescription(step)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 465,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 23\n                  }, this), step.status === 'failed' && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-red-600 text-sm\",\n                    children: getStepDescription(step)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 472,\n                    columnNumber: 25\n                  }, this), step.status === 'failed' && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: retryFromFailed,\n                      disabled: isRunning,\n                      className: \"p-1.5 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-full transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 active:translate-y-0\",\n                      title: \"\\u91CD\\u8BD5\",\n                      children: /*#__PURE__*/_jsxDEV(RefreshCw, {\n                        className: \"w-3 h-3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 486,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 480,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 23\n                  }, this), step.status === 'completed' && workflowStatus === 'completed' && index === steps.length - 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-3 -ml-12 flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"inline-block bg-green-50 border border-green-200 rounded-full px-4 py-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-green-700 text-sm whitespace-nowrap\",\n                        children: \"\\u5DF2\\u5B8C\\u6210\\u5F53\\u524D\\u6240\\u6709\\u8BA1\\u5212\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 496,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 495,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: savePlan,\n                      className: \"p-1 text-gray-600 hover:text-blue-600 transition-all duration-200 hover:scale-110\",\n                      title: \"\\u4FDD\\u5B58\\u8BA1\\u5212\",\n                      children: /*#__PURE__*/_jsxDEV(Save, {\n                        className: \"w-4 h-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 507,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 502,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 493,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 19\n              }, this)]\n            }, step.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-8 pt-4 border-t border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [workflowStatus === 'idle' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: executeWorkflow,\n                  disabled: isRunning,\n                  className: \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Play, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 529,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: isMultiStore ? '开始多店铺执行' : '开始执行'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 530,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 524,\n                  columnNumber: 21\n                }, this), !isMultiStore && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: switchToMultiStore,\n                  className: \"bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u591A\\u5E97\\u94FA\\u6F14\\u793A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 537,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 533,\n                  columnNumber: 23\n                }, this), isMultiStore && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: switchToSingleStore,\n                  className: \"bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u5355\\u5E97\\u94FA\\u6A21\\u5F0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 545,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: simulateFailure,\n                  className: \"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 552,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u6A21\\u62DF\\u5931\\u8D25\\u72B6\\u6001\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 553,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true), workflowStatus === 'running' && /*#__PURE__*/_jsxDEV(\"button\", {\n                disabled: true,\n                className: \"bg-gradient-to-r from-gray-400 to-gray-500 text-white px-6 py-3 rounded-xl text-sm font-medium cursor-not-allowed flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(Loader2, {\n                  className: \"w-4 h-4 animate-spin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u6267\\u884C\\u4E2D...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 564,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 19\n              }, this), workflowStatus === 'completed' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: resetWorkflow,\n                  className: \"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 574,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u6267\\u884C\\u5B8C\\u6210\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 575,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: simulateFailure,\n                  className: \"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 581,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u6A21\\u62DF\\u5931\\u8D25\\u72B6\\u6001\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 582,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 577,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true), workflowStatus === 'failed' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: retryFromFailed,\n                  disabled: isRunning,\n                  className: \"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 594,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u91CD\\u8BD5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 595,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 589,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: resetWorkflow,\n                  className: \"bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0\",\n                  children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 597,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: resetWorkflow,\n              className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:shadow-sm\",\n              title: \"\\u91CD\\u65B0\\u5F00\\u59CB\",\n              children: /*#__PURE__*/_jsxDEV(RefreshCw, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 13\n          }, this), workflowStatus === 'failed' && failedStep !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200/50 rounded-xl p-6 shadow-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg\",\n                children: /*#__PURE__*/_jsxDEV(AlertCircle, {\n                  className: \"w-5 h-5 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 625,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-red-700 font-semibold text-base mb-1\",\n                  children: \"\\u6267\\u884C\\u4E2D\\u65AD\\uFF0C\\u9700\\u8981\\u5904\\u7406\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 628,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-red-600 text-sm\",\n                  children: [\"\\u7B2C \", failedStep + 1, \" \\u6B65\\u51FA\\u73B0\\u95EE\\u9898\\uFF0C\\u53EF\\u70B9\\u51FB\\u91CD\\u8BD5\\u7EE7\\u7EED\\u6267\\u884C\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 631,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 627,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 7\n    }, this), showToast && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-4 right-4 z-50 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg flex items-center space-x-2 animate-pulse\",\n      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n        className: \"w-5 h-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 645,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"\\u4FDD\\u5B58\\u8BA1\\u5212\\u6210\\u529F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 646,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 644,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 314,\n    columnNumber: 5\n  }, this);\n};\n_s(TemuWorkflowDemo, \"3CyNPpbP/LrZReOhTPU1z51RefM=\");\n_c = TemuWorkflowDemo;\nexport default TemuWorkflowDemo;\nvar _c;\n$RefreshReg$(_c, \"TemuWorkflowDemo\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Play", "RefreshCw", "CheckCircle", "XCircle", "AlertCircle", "Loader2", "Save", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TemuWorkflowDemo", "_s", "currentStep", "setCurrentStep", "isRunning", "setIsRunning", "workflowStatus", "setWorkflowStatus", "visibleSteps", "setVisibleSteps", "failedStep", "setFailedStep", "showToast", "setShowToast", "productProcessingStep", "setProductProcessingStep", "startTime", "setStartTime", "elapsedTime", "setElapsedTime", "intervalRef", "isMultiStore", "setIsMultiStore", "workflowSteps", "id", "name", "runningDescription", "completedDescription", "duration", "status", "progressSteps", "multiStoreWorkflowSteps", "steps", "setSteps", "current", "setInterval", "now", "Date", "elapsed", "Math", "floor", "clearInterval", "formatTime", "seconds", "mins", "secs", "toString", "padStart", "getStepDescription", "step", "executeWorkflow", "startStep", "prev", "map", "i", "length", "index", "Promise", "resolve", "setTimeout", "j", "shouldFail", "random", "currentElapsed", "retryFromFailed", "resetWorkflow", "currentWorkflowSteps", "savePlan", "switchToMultiStore", "switchToSingleStore", "simulateFailure", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "slice", "nextStep", "shouldShowConnector", "connectorColor", "style", "height", "onClick", "disabled", "title", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowDemo.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { Play, RefreshCw, CheckCircle, XCircle, AlertCircle, Loader2, Save } from 'lucide-react';\n\nconst TemuWorkflowDemo = () => {\n  const [currentStep, setCurrentStep] = useState(0);\n  const [isRunning, setIsRunning] = useState(false);\n  const [workflowStatus, setWorkflowStatus] = useState('idle'); // idle, running, completed, failed\n  const [visibleSteps, setVisibleSteps] = useState(0); // 控制显示的步骤数量\n  const [failedStep, setFailedStep] = useState(null); // 记录失败的步骤\n  const [showToast, setShowToast] = useState(false); // 控制toast显示\n  const [productProcessingStep, setProductProcessingStep] = useState(0); // 商品加权进度步骤\n  const [startTime, setStartTime] = useState(null); // 执行开始时间\n  const [elapsedTime, setElapsedTime] = useState(0); // 累计执行时间（秒）\n  const intervalRef = useRef(null); // 定时器引用\n  const [isMultiStore, setIsMultiStore] = useState(false); // 是否为多店铺模式\n\n  // 工作流步骤定义\n  const workflowSteps = [\n    {\n      id: 'login',\n      name: '登录',\n      runningDescription: '正在验证登录信息...',\n      completedDescription: '已完成登录【Temu账号】名下的店铺1',\n      duration: 2000,\n      status: 'pending'\n    },\n    {\n      id: 'product_processing',\n      name: '商品流量加速',\n      runningDescription: '正在为商品流量加速...',\n      completedDescription: '全部商品流量加速任务已完成！所有商品已按规则完成流量加速。',\n      duration: 4000,\n      status: 'pending',\n      progressSteps: [\n        '商品流量加速任务已启动：正在按照预设规则对所有商品进行处理，单个商品处理时间约20-30秒',\n        '商品SPU ID: 204845849 已开启30天普通流量加速加权。开始处理下一个商品...'\n      ]\n    },\n    {\n      id: 'result',\n      name: '结果',\n      runningDescription: '正在生成执行结果...',\n      completedDescription: '本次任务执行完毕。共成功处理48个商品。另有2个商品未能成功，原因如下：【商品名称A】因AI判断其价格为6.2，高于5的设定而被跳过；【商品名称B】因页面长时间无法打开而执行失败。',\n      duration: 1000,\n      status: 'pending'\n    }\n  ];\n\n  // 多店铺工作流步骤定义\n  const multiStoreWorkflowSteps = [\n    {\n      id: 'login_store1',\n      name: '登录店铺1',\n      runningDescription: '正在验证店铺1登录信息...',\n      completedDescription: '已完成登录【Temu账号】名下的店铺1',\n      duration: 2000,\n      status: 'pending'\n    },\n    {\n      id: 'traffic_acceleration_store1',\n      name: '流量加速店铺1',\n      runningDescription: '正在为店铺1设置流量加速...',\n      completedDescription: '已完成店铺1的流量加速设置',\n      duration: 3000,\n      status: 'pending'\n    },\n    {\n      id: 'login_store2',\n      name: '登录店铺2',\n      runningDescription: '正在验证店铺2登录信息...',\n      completedDescription: '已完成登录【Temu账号】名下的店铺2',\n      duration: 2000,\n      status: 'pending'\n    },\n    {\n      id: 'product_processing_store2',\n      name: '商品加权店铺2',\n      runningDescription: '正在为店铺2商品设置流量加权...',\n      completedDescription: '已完成店铺2商品的流量加权设置',\n      duration: 4000,\n      status: 'pending',\n      progressSteps: [\n        '正在处理店铺2商品',\n        '店铺2商品已开启30天的普通流量加速加权',\n        '已完成店铺2商品流量加权设置'\n      ]\n    },\n    {\n      id: 'multi_store_result',\n      name: '汇总结果',\n      runningDescription: '正在生成多店铺执行结果...',\n      completedDescription: '多店铺任务执行完毕',\n      duration: 1000,\n      status: 'pending'\n    }\n  ];\n\n  const [steps, setSteps] = useState(workflowSteps);\n\n  // 时间统计效果\n  useEffect(() => {\n    if (workflowStatus === 'running' && startTime) {\n      intervalRef.current = setInterval(() => {\n        const now = Date.now();\n        const elapsed = Math.floor((now - startTime) / 1000) + elapsedTime;\n        setElapsedTime(elapsed);\n      }, 1000);\n    } else {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n        intervalRef.current = null;\n      }\n    }\n\n    return () => {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n    };\n  }, [workflowStatus, startTime]);\n\n  // 格式化时间显示\n  const formatTime = (seconds) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // 获取步骤描述\n  const getStepDescription = (step) => {\n    if (step.status === 'running') {\n      // 商品加权步骤显示动态进度文案（支持单店铺和多店铺）\n      if ((step.id === 'product_processing' || step.id === 'product_processing_store2') && step.progressSteps) {\n        return step.progressSteps[productProcessingStep] || step.runningDescription;\n      }\n      return step.runningDescription;\n    } else if (step.status === 'completed') {\n      return step.completedDescription;\n    } else if (step.status === 'failed') {\n      return `执行失败：${step.runningDescription}`;\n    }\n    return '等待执行...';\n  };\n\n  // 模拟工作流执行\n  const executeWorkflow = async () => {\n    setIsRunning(true);\n    setWorkflowStatus('running');\n    setFailedStep(null);\n\n    // 启动计时器（如果是重试，保持累计时间）\n    const now = Date.now();\n    setStartTime(now);\n\n    // 从当前失败步骤或第一步开始\n    const startStep = failedStep !== null ? failedStep : 0;\n    setCurrentStep(startStep);\n\n    // 如果是重新开始，重置步骤状态\n    if (failedStep === null) {\n      setVisibleSteps(0);\n      setSteps(prev => prev.map(step => ({ ...step, status: 'pending' })));\n    }\n\n    for (let i = startStep; i < steps.length; i++) {\n      setCurrentStep(i);\n\n      // 显示当前步骤（如果还未显示）\n      if (i + 1 > visibleSteps) {\n        setVisibleSteps(i + 1);\n        // 确保新显示的步骤处于pending状态\n        setSteps(prev => prev.map((step, index) =>\n          index === i ? { ...step, status: 'pending' } : step\n        ));\n        await new Promise(resolve => setTimeout(resolve, 300)); // 短暂延迟显示步骤\n      }\n\n      // 设置当前步骤为运行中\n      setSteps(prev => prev.map((step, index) =>\n        index === i ? { ...step, status: 'running' } : step\n      ));\n\n      // 商品加权步骤的动态文案切换（支持单店铺和多店铺）\n      if ((steps[i].id === 'product_processing' || steps[i].id === 'product_processing_store2') && steps[i].progressSteps) {\n        const progressSteps = steps[i].progressSteps;\n        for (let j = 0; j < progressSteps.length; j++) {\n          setProductProcessingStep(j);\n          await new Promise(resolve => setTimeout(resolve, steps[i].duration / progressSteps.length));\n        }\n      } else {\n        // 等待步骤完成\n        await new Promise(resolve => setTimeout(resolve, steps[i].duration));\n      }\n\n      // 随机失败演示（15%概率，在第2或第3步）\n      const shouldFail = Math.random() < 0.15 && (i === 1 || i === 2);\n\n      if (shouldFail) {\n        setSteps(prev => prev.map((step, index) =>\n          index === i ? { ...step, status: 'failed' } : step\n        ));\n        setWorkflowStatus('failed');\n        setFailedStep(i);\n        setIsRunning(false);\n\n        // 失败时保存当前累计时间\n        if (startTime) {\n          const now = Date.now();\n          const currentElapsed = Math.floor((now - startTime) / 1000);\n          setElapsedTime(prev => prev + currentElapsed);\n          setStartTime(null);\n        }\n        return;\n      }\n\n      // 设置步骤为完成\n      setSteps(prev => prev.map((step, index) =>\n        index === i ? { ...step, status: 'completed' } : step\n      ));\n\n      // 完成一步后显示下一步（如果不是最后一步）\n      if (i < steps.length - 1) {\n        setVisibleSteps(i + 2);\n        // 确保下一步显示为pending状态\n        setSteps(prev => prev.map((step, index) =>\n          index === i + 1 ? { ...step, status: 'pending' } : step\n        ));\n        await new Promise(resolve => setTimeout(resolve, 500)); // 延迟显示下一步\n      }\n    }\n\n    setWorkflowStatus('completed');\n    setFailedStep(null);\n    setIsRunning(false);\n\n    // 完成时保存最终累计时间\n    if (startTime) {\n      const now = Date.now();\n      const currentElapsed = Math.floor((now - startTime) / 1000);\n      setElapsedTime(prev => prev + currentElapsed);\n      setStartTime(null);\n    }\n  };\n\n  // 重试失败的步骤\n  const retryFromFailed = () => {\n    if (failedStep !== null) {\n      // 重置失败步骤的状态\n      setSteps(prev => prev.map((step, index) => \n        index === failedStep ? { ...step, status: 'pending' } : step\n      ));\n      executeWorkflow();\n    }\n  };\n\n  // 重置整个工作流\n  const resetWorkflow = () => {\n    setIsRunning(false);\n    setWorkflowStatus('idle');\n    setCurrentStep(0);\n    setVisibleSteps(0);\n    setFailedStep(null);\n    setProductProcessingStep(0);\n    setStartTime(null);\n    setElapsedTime(0);\n    const currentWorkflowSteps = isMultiStore ? multiStoreWorkflowSteps : workflowSteps;\n    setSteps(currentWorkflowSteps.map(step => ({ ...step, status: 'pending' })));\n  };\n\n  // 保存计划\n  const savePlan = () => {\n    setShowToast(true);\n    setTimeout(() => setShowToast(false), 3000);\n  };\n\n  // 切换到多店铺模式\n  const switchToMultiStore = () => {\n    setIsMultiStore(true);\n    setSteps(multiStoreWorkflowSteps.map(step => ({ ...step, status: 'pending' })));\n    setWorkflowStatus('idle');\n    setVisibleSteps(0);\n    setFailedStep(null);\n    setProductProcessingStep(0);\n    setStartTime(null);\n    setElapsedTime(0);\n  };\n\n  // 切换到单店铺模式\n  const switchToSingleStore = () => {\n    setIsMultiStore(false);\n    setSteps(workflowSteps.map(step => ({ ...step, status: 'pending' })));\n    setWorkflowStatus('idle');\n    setVisibleSteps(0);\n    setFailedStep(null);\n    setProductProcessingStep(0);\n    setStartTime(null);\n    setElapsedTime(0);\n  };\n\n  // 模拟失败状态\n  const simulateFailure = () => {\n    setVisibleSteps(3);\n    setSteps(prev => prev.map((step, index) => {\n      if (index === 0) return { ...step, status: 'completed' };\n      if (index === 1) return { ...step, status: 'completed' };\n      if (index === 2) return { ...step, status: 'failed' };\n      return step;\n    }));\n    setWorkflowStatus('failed');\n    setFailedStep(2);\n  };\n\n  return (\n    <div className=\"w-full\">\n      {/* AI对话框容器 */}\n      <div className=\"bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg overflow-hidden border border-white/20\">\n        {/* 对话框头部 */}\n        <div className=\"bg-blue-500 text-white p-4\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\">\n              <span className=\"text-sm font-bold\">AI</span>\n            </div>\n            <div>\n              <h3 className=\"font-semibold\">Temu自动化助手</h3>\n              <p className=\"text-blue-100 text-sm\">流量加速自动化执行中...</p>\n            </div>\n          </div>\n        </div>\n\n        {/* 对话内容 */}\n        <div className=\"p-6\">\n          {/* AI消息 */}\n          <div className=\"mb-6\">\n            <div className=\"bg-gray-100 rounded-lg p-4 mb-4\">\n              <p className=\"text-gray-800 mb-2\">好的，开始执行计划</p>\n              <div className=\"text-sm text-gray-600 bg-white rounded-lg p-3 border\">\n                <span className=\"font-medium\">执行配置：</span>普通流量加权档位，价格范围4-6美元，时效30天\n              </div>\n            </div>\n          </div>\n\n          {/* 执行开始提示区域 */}\n          {(workflowStatus === 'running' || workflowStatus === 'completed') && (\n            <div className=\"mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center\">\n                    {workflowStatus === 'running' ? (\n                      <Loader2 className=\"w-3 h-3 text-white animate-spin\" />\n                    ) : (\n                      <CheckCircle className=\"w-3 h-3 text-white\" />\n                    )}\n                  </div>\n                  <span className=\"text-blue-800 font-medium\">好的，开始执行计划</span>\n                </div>\n                <div className=\"text-sm text-blue-600\">\n                  <span className=\"font-medium\">执行时间：</span>\n                  <span className=\"font-mono\">{formatTime(elapsedTime)}</span>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* 优化后的时间线步骤列表 - 完美连接线 */}\n          <div className=\"relative\">\n            {steps.slice(0, visibleSteps).map((step, index) => {\n              const nextStep = steps[index + 1];\n              const shouldShowConnector = index < visibleSteps - 1 && index < steps.length - 1;\n              const connectorColor = step.status === 'completed' ? 'bg-green-500' : 'bg-gray-300';\n\n              return (\n                <div key={step.id} className=\"relative\">\n                  {/* 连接线 - 只在步骤完成后显示绿色，否则显示灰色 */}\n                  {shouldShowConnector && (\n                    <div\n                      className={`absolute left-4 top-8 w-0.5 transition-all duration-500 z-0 ${connectorColor}`}\n                      style={{\n                        height: '3rem' // 固定高度，只连接到下一个步骤的起始位置\n                      }}\n                    ></div>\n                  )}\n\n                  {/* 步骤内容容器 */}\n                  <div className=\"flex items-start space-x-4 relative z-10 pb-6\">\n                    {/* 状态图标容器 - 优化层级确保完美覆盖 */}\n                    <div className=\"flex-shrink-0 relative\">\n                      {/* 图标背景圆圈 - 确保完全覆盖连接线 */}\n                      <div className=\"w-8 h-8 bg-white rounded-full absolute inset-0 z-10 border border-gray-100\"></div>\n\n                      {/* 状态图标 - 最高层级 */}\n                      <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 relative z-20 shadow-sm ${\n                        step.status === 'completed' ? 'bg-green-500 text-white' :\n                        step.status === 'failed' ? 'bg-red-500 text-white' :\n                        step.status === 'running' ? 'bg-blue-500 text-white' :\n                        step.status === 'pending' ? 'bg-blue-500 text-white' :\n                        'bg-gray-300 text-gray-600'\n                      }`}>\n                        {step.status === 'completed' && <CheckCircle className=\"w-5 h-5\" />}\n                        {step.status === 'failed' && <XCircle className=\"w-5 h-5\" />}\n                        {step.status === 'running' && <Loader2 className=\"w-5 h-5 animate-spin\" />}\n                        {step.status === 'pending' && <Loader2 className=\"w-5 h-5 animate-spin\" />}\n                      </div>\n                    </div>\n\n                  {/* 步骤内容 */}\n                  <div className=\"flex-1 min-w-0 pt-1\">\n                    <div className=\"mb-2\">\n                      <h4 className=\"text-lg font-medium text-gray-900\">{step.name}</h4>\n                    </div>\n\n                    {/* 只在非失败状态时显示描述 */}\n                    {step.status !== 'failed' && (\n                      <div className=\"text-sm text-gray-600 leading-relaxed\">\n                        {(step.id === 'result' || step.id === 'multi_store_result') && step.status === 'completed' ? (\n                          <div>\n                            {step.id === 'multi_store_result' ? (\n                              // 多店铺汇总结果\n                              <div>\n                                <div className=\"font-medium mb-2\">本次任务执行完毕</div>\n                                <div className=\"mb-2\">📊 处理汇总：</div>\n                                <div className=\"mb-2\">- 店铺1：成功处理 <span className=\"font-semibold text-green-600\">48个商品</span></div>\n                                <div className=\"mb-2\">- 店铺2：成功处理 <span className=\"font-semibold text-green-600\">30个商品</span></div>\n                                <div className=\"mb-2\">- 总计：<span className=\"font-semibold text-blue-600\">78个商品</span>处理完成</div>\n\n                                <div className=\"mb-2\">⚠️ 异常商品详情：</div>\n\n                                <div className=\"mb-2\">【店铺1】异常统计：</div>\n                                <div className=\"mb-1\">├─ 不符合条件(2个)：</div>\n                                <div className=\"space-y-1 pl-4 border-l-2 border-gray-200 mb-2\">\n                                  <div>• SPU ID: 204845842</div>\n                                  <div>• SPU ID: 204845843</div>\n                                </div>\n                                <div className=\"mb-1\">└─ 处理失败(2个)：</div>\n                                <div className=\"space-y-1 pl-4 border-l-2 border-gray-200 mb-3\">\n                                  <div>• SPU ID: 204845849</div>\n                                  <div>• SPU ID: 204845844</div>\n                                </div>\n\n                                <div className=\"mb-2\">【店铺2】异常统计：</div>\n                                <div className=\"mb-1\">├─ 不符合条件(2个)：</div>\n                                <div className=\"space-y-1 pl-4 border-l-2 border-gray-200 mb-2\">\n                                  <div>• SPU ID: 204845842</div>\n                                  <div>• SPU ID: 204845843</div>\n                                </div>\n                                <div className=\"mb-1\">└─ 处理失败(2个)：</div>\n                                <div className=\"space-y-1 pl-4 border-l-2 border-gray-200\">\n                                  <div>• SPU ID: 204845849</div>\n                                  <div>• SPU ID: 204845844</div>\n                                </div>\n                              </div>\n                            ) : (\n                              // 单店铺结果\n                              <div>\n                                <div className=\"font-medium mb-2\">本次任务执行完毕</div>\n                                <div className=\"mb-2\">共成功处理 <span className=\"font-semibold text-green-600\">48个商品</span></div>\n                                <div className=\"mb-2\">另有 <span className=\"font-semibold text-orange-600\">2个商品</span> 未能成功，原因如下：</div>\n                                <div className=\"space-y-1 pl-4 border-l-2 border-gray-200\">\n                                  <div>• <span className=\"font-medium\">商品名称A</span>：因AI判断其价格为6.2元，高于5元设定而被跳过</div>\n                                  <div>• <span className=\"font-medium\">商品名称B</span>：因页面长时间无法打开而执行失败</div>\n                                </div>\n                              </div>\n                            )}\n                          </div>\n                        ) : (\n                          <p>{getStepDescription(step)}</p>\n                        )}\n                      </div>\n                    )}\n\n                    {/* 失败时的状态消息容器 */}\n                    {step.status === 'failed' && (\n                        <p className=\"text-red-600 text-sm\">\n                          {getStepDescription(step)}\n                        </p>\n                    )}\n\n                    {/* 重试按钮 - 在失败状态消息容器下方，与步骤标题左对齐 */}\n                    {step.status === 'failed' && (\n                      <div className=\"mt-3\">\n                        <button\n                          onClick={retryFromFailed}\n                          disabled={isRunning}\n                          className=\"p-1.5 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-full transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 active:translate-y-0\"\n                          title=\"重试\"\n                        >\n                          <RefreshCw className=\"w-3 h-3\" />\n                        </button>\n                      </div>\n                    )}\n\n                    {/* 成功状态：椭圆形消息容器和保存按钮布局 */}\n                    {step.status === 'completed' && workflowStatus === 'completed' && index === steps.length - 1 && (\n                      <div className=\"mt-3 -ml-12 flex items-center space-x-2\">\n                        {/* 成功状态消息容器 - 椭圆形态，宽度跟随文字，左对齐到绿色图标 */}\n                        <div className=\"inline-block bg-green-50 border border-green-200 rounded-full px-4 py-2\">\n                          <p className=\"text-green-700 text-sm whitespace-nowrap\">\n                           已完成当前所有计划\n                          </p>\n                        </div>\n\n                        {/* 保存按钮 - 纯图标，在椭圆形容器右侧 */}\n                        <button\n                          onClick={savePlan}\n                          className=\"p-1 text-gray-600 hover:text-blue-600 transition-all duration-200 hover:scale-110\"\n                          title=\"保存计划\"\n                        >\n                          <Save className=\"w-4 h-4\" />\n                        </button>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            );\n            })}\n          </div>\n\n          {/* 操作按钮区域 */}\n          <div className=\"mt-8 pt-4 border-t border-gray-200\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                {workflowStatus === 'idle' && (\n                  <>\n                    <button\n                      onClick={executeWorkflow}\n                      disabled={isRunning}\n                      className=\"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                    >\n                      <Play className=\"w-4 h-4\" />\n                      <span>{isMultiStore ? '开始多店铺执行' : '开始执行'}</span>\n                    </button>\n                    {!isMultiStore && (\n                      <button\n                        onClick={switchToMultiStore}\n                        className=\"bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                      >\n                        <span>多店铺演示</span>\n                      </button>\n                    )}\n                    {isMultiStore && (\n                      <button\n                        onClick={switchToSingleStore}\n                        className=\"bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                      >\n                        <span>单店铺模式</span>\n                      </button>\n                    )}\n                    <button\n                      onClick={simulateFailure}\n                      className=\"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                    >\n                      <AlertCircle className=\"w-4 h-4\" />\n                      <span>模拟失败状态</span>\n                    </button>\n                  </>\n                )}\n\n                {workflowStatus === 'running' && (\n                  <button\n                    disabled\n                    className=\"bg-gradient-to-r from-gray-400 to-gray-500 text-white px-6 py-3 rounded-xl text-sm font-medium cursor-not-allowed flex items-center space-x-2\"\n                  >\n                    <Loader2 className=\"w-4 h-4 animate-spin\" />\n                    <span>执行中...</span>\n                  </button>\n                )}\n\n                {workflowStatus === 'completed' && (\n                  <>\n                    <button\n                      onClick={resetWorkflow}\n                      className=\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                    >\n                      <CheckCircle className=\"w-4 h-4\" />\n                      <span>执行完成</span>\n                    </button>\n                    <button\n                      onClick={simulateFailure}\n                      className=\"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                    >\n                      <AlertCircle className=\"w-4 h-4\" />\n                      <span>模拟失败状态</span>\n                    </button>\n                  </>\n                )}\n\n                {workflowStatus === 'failed' && (\n                  <div className=\"flex items-center space-x-3\">\n                    <button\n                      onClick={retryFromFailed}\n                      disabled={isRunning}\n                      className=\"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                    >\n                      <RefreshCw className=\"w-4 h-4\" />\n                      <span>重试</span>\n                    </button>\n                    <button\n                      onClick={resetWorkflow}\n                      className=\"bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0\"\n                    >\n                      重新开始\n                    </button>\n                  </div>\n                )}\n\n\n              </div>\n\n              {/* 刷新按钮 */}\n              <button\n                onClick={resetWorkflow}\n                className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:shadow-sm\"\n                title=\"重新开始\"\n              >\n                <RefreshCw className=\"w-4 h-4\" />\n              </button>\n            </div>\n\n   \n\n            {workflowStatus === 'failed' && failedStep !== null && (\n              <div className=\"mt-6 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200/50 rounded-xl p-6 shadow-sm\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"w-10 h-10 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg\">\n                    <AlertCircle className=\"w-5 h-5 text-white\" />\n                  </div>\n                  <div>\n                    <div className=\"text-red-700 font-semibold text-base mb-1\">\n                      执行中断，需要处理\n                    </div>\n                    <div className=\"text-red-600 text-sm\">\n                      第 {failedStep + 1} 步出现问题，可点击重试继续执行\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Toast 通知 */}\n      {showToast && (\n        <div className=\"fixed top-4 right-4 z-50 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg flex items-center space-x-2 animate-pulse\">\n          <CheckCircle className=\"w-5 h-5\" />\n          <span>保存计划成功</span>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TemuWorkflowDemo;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,IAAI,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,WAAW,EAAEC,OAAO,EAAEC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjG,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoB,cAAc,EAAEC,iBAAiB,CAAC,GAAGrB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACrD,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC4B,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACvE,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAClD,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACnD,MAAMkC,WAAW,GAAGhC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;EAClC,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEzD;EACA,MAAMqC,aAAa,GAAG,CACpB;IACEC,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,IAAI;IACVC,kBAAkB,EAAE,aAAa;IACjCC,oBAAoB,EAAE,qBAAqB;IAC3CC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE;EACV,CAAC,EACD;IACEL,EAAE,EAAE,oBAAoB;IACxBC,IAAI,EAAE,QAAQ;IACdC,kBAAkB,EAAE,cAAc;IAClCC,oBAAoB,EAAE,+BAA+B;IACrDC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,SAAS;IACjBC,aAAa,EAAE,CACb,+CAA+C,EAC/C,iDAAiD;EAErD,CAAC,EACD;IACEN,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,IAAI;IACVC,kBAAkB,EAAE,aAAa;IACjCC,oBAAoB,EAAE,4FAA4F;IAClHC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE;EACV,CAAC,CACF;;EAED;EACA,MAAME,uBAAuB,GAAG,CAC9B;IACEP,EAAE,EAAE,cAAc;IAClBC,IAAI,EAAE,OAAO;IACbC,kBAAkB,EAAE,gBAAgB;IACpCC,oBAAoB,EAAE,qBAAqB;IAC3CC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE;EACV,CAAC,EACD;IACEL,EAAE,EAAE,6BAA6B;IACjCC,IAAI,EAAE,SAAS;IACfC,kBAAkB,EAAE,iBAAiB;IACrCC,oBAAoB,EAAE,eAAe;IACrCC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE;EACV,CAAC,EACD;IACEL,EAAE,EAAE,cAAc;IAClBC,IAAI,EAAE,OAAO;IACbC,kBAAkB,EAAE,gBAAgB;IACpCC,oBAAoB,EAAE,qBAAqB;IAC3CC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE;EACV,CAAC,EACD;IACEL,EAAE,EAAE,2BAA2B;IAC/BC,IAAI,EAAE,SAAS;IACfC,kBAAkB,EAAE,mBAAmB;IACvCC,oBAAoB,EAAE,iBAAiB;IACvCC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,SAAS;IACjBC,aAAa,EAAE,CACb,WAAW,EACX,sBAAsB,EACtB,gBAAgB;EAEpB,CAAC,EACD;IACEN,EAAE,EAAE,oBAAoB;IACxBC,IAAI,EAAE,MAAM;IACZC,kBAAkB,EAAE,gBAAgB;IACpCC,oBAAoB,EAAE,WAAW;IACjCC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAM,CAACG,KAAK,EAAEC,QAAQ,CAAC,GAAG/C,QAAQ,CAACqC,aAAa,CAAC;;EAEjD;EACApC,SAAS,CAAC,MAAM;IACd,IAAImB,cAAc,KAAK,SAAS,IAAIU,SAAS,EAAE;MAC7CI,WAAW,CAACc,OAAO,GAAGC,WAAW,CAAC,MAAM;QACtC,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;QACtB,MAAME,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACJ,GAAG,GAAGpB,SAAS,IAAI,IAAI,CAAC,GAAGE,WAAW;QAClEC,cAAc,CAACmB,OAAO,CAAC;MACzB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM;MACL,IAAIlB,WAAW,CAACc,OAAO,EAAE;QACvBO,aAAa,CAACrB,WAAW,CAACc,OAAO,CAAC;QAClCd,WAAW,CAACc,OAAO,GAAG,IAAI;MAC5B;IACF;IAEA,OAAO,MAAM;MACX,IAAId,WAAW,CAACc,OAAO,EAAE;QACvBO,aAAa,CAACrB,WAAW,CAACc,OAAO,CAAC;MACpC;IACF,CAAC;EACH,CAAC,EAAE,CAAC5B,cAAc,EAAEU,SAAS,CAAC,CAAC;;EAE/B;EACA,MAAM0B,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,IAAI,GAAGL,IAAI,CAACC,KAAK,CAACG,OAAO,GAAG,EAAE,CAAC;IACrC,MAAME,IAAI,GAAGF,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAClF,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAIC,IAAI,IAAK;IACnC,IAAIA,IAAI,CAACpB,MAAM,KAAK,SAAS,EAAE;MAC7B;MACA,IAAI,CAACoB,IAAI,CAACzB,EAAE,KAAK,oBAAoB,IAAIyB,IAAI,CAACzB,EAAE,KAAK,2BAA2B,KAAKyB,IAAI,CAACnB,aAAa,EAAE;QACvG,OAAOmB,IAAI,CAACnB,aAAa,CAAChB,qBAAqB,CAAC,IAAImC,IAAI,CAACvB,kBAAkB;MAC7E;MACA,OAAOuB,IAAI,CAACvB,kBAAkB;IAChC,CAAC,MAAM,IAAIuB,IAAI,CAACpB,MAAM,KAAK,WAAW,EAAE;MACtC,OAAOoB,IAAI,CAACtB,oBAAoB;IAClC,CAAC,MAAM,IAAIsB,IAAI,CAACpB,MAAM,KAAK,QAAQ,EAAE;MACnC,OAAO,QAAQoB,IAAI,CAACvB,kBAAkB,EAAE;IAC1C;IACA,OAAO,SAAS;EAClB,CAAC;;EAED;EACA,MAAMwB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC7C,YAAY,CAAC,IAAI,CAAC;IAClBE,iBAAiB,CAAC,SAAS,CAAC;IAC5BI,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,MAAMyB,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACtBnB,YAAY,CAACmB,GAAG,CAAC;;IAEjB;IACA,MAAMe,SAAS,GAAGzC,UAAU,KAAK,IAAI,GAAGA,UAAU,GAAG,CAAC;IACtDP,cAAc,CAACgD,SAAS,CAAC;;IAEzB;IACA,IAAIzC,UAAU,KAAK,IAAI,EAAE;MACvBD,eAAe,CAAC,CAAC,CAAC;MAClBwB,QAAQ,CAACmB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACJ,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEpB,MAAM,EAAE;MAAU,CAAC,CAAC,CAAC,CAAC;IACtE;IAEA,KAAK,IAAIyB,CAAC,GAAGH,SAAS,EAAEG,CAAC,GAAGtB,KAAK,CAACuB,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7CnD,cAAc,CAACmD,CAAC,CAAC;;MAEjB;MACA,IAAIA,CAAC,GAAG,CAAC,GAAG9C,YAAY,EAAE;QACxBC,eAAe,CAAC6C,CAAC,GAAG,CAAC,CAAC;QACtB;QACArB,QAAQ,CAACmB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KACpCA,KAAK,KAAKF,CAAC,GAAG;UAAE,GAAGL,IAAI;UAAEpB,MAAM,EAAE;QAAU,CAAC,GAAGoB,IACjD,CAAC,CAAC;QACF,MAAM,IAAIQ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;MAC1D;;MAEA;MACAzB,QAAQ,CAACmB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KACpCA,KAAK,KAAKF,CAAC,GAAG;QAAE,GAAGL,IAAI;QAAEpB,MAAM,EAAE;MAAU,CAAC,GAAGoB,IACjD,CAAC,CAAC;;MAEF;MACA,IAAI,CAACjB,KAAK,CAACsB,CAAC,CAAC,CAAC9B,EAAE,KAAK,oBAAoB,IAAIQ,KAAK,CAACsB,CAAC,CAAC,CAAC9B,EAAE,KAAK,2BAA2B,KAAKQ,KAAK,CAACsB,CAAC,CAAC,CAACxB,aAAa,EAAE;QACnH,MAAMA,aAAa,GAAGE,KAAK,CAACsB,CAAC,CAAC,CAACxB,aAAa;QAC5C,KAAK,IAAI8B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9B,aAAa,CAACyB,MAAM,EAAEK,CAAC,EAAE,EAAE;UAC7C7C,wBAAwB,CAAC6C,CAAC,CAAC;UAC3B,MAAM,IAAIH,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE1B,KAAK,CAACsB,CAAC,CAAC,CAAC1B,QAAQ,GAAGE,aAAa,CAACyB,MAAM,CAAC,CAAC;QAC7F;MACF,CAAC,MAAM;QACL;QACA,MAAM,IAAIE,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE1B,KAAK,CAACsB,CAAC,CAAC,CAAC1B,QAAQ,CAAC,CAAC;MACtE;;MAEA;MACA,MAAMiC,UAAU,GAAGtB,IAAI,CAACuB,MAAM,CAAC,CAAC,GAAG,IAAI,KAAKR,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,CAAC;MAE/D,IAAIO,UAAU,EAAE;QACd5B,QAAQ,CAACmB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KACpCA,KAAK,KAAKF,CAAC,GAAG;UAAE,GAAGL,IAAI;UAAEpB,MAAM,EAAE;QAAS,CAAC,GAAGoB,IAChD,CAAC,CAAC;QACF1C,iBAAiB,CAAC,QAAQ,CAAC;QAC3BI,aAAa,CAAC2C,CAAC,CAAC;QAChBjD,YAAY,CAAC,KAAK,CAAC;;QAEnB;QACA,IAAIW,SAAS,EAAE;UACb,MAAMoB,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;UACtB,MAAM2B,cAAc,GAAGxB,IAAI,CAACC,KAAK,CAAC,CAACJ,GAAG,GAAGpB,SAAS,IAAI,IAAI,CAAC;UAC3DG,cAAc,CAACiC,IAAI,IAAIA,IAAI,GAAGW,cAAc,CAAC;UAC7C9C,YAAY,CAAC,IAAI,CAAC;QACpB;QACA;MACF;;MAEA;MACAgB,QAAQ,CAACmB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KACpCA,KAAK,KAAKF,CAAC,GAAG;QAAE,GAAGL,IAAI;QAAEpB,MAAM,EAAE;MAAY,CAAC,GAAGoB,IACnD,CAAC,CAAC;;MAEF;MACA,IAAIK,CAAC,GAAGtB,KAAK,CAACuB,MAAM,GAAG,CAAC,EAAE;QACxB9C,eAAe,CAAC6C,CAAC,GAAG,CAAC,CAAC;QACtB;QACArB,QAAQ,CAACmB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KACpCA,KAAK,KAAKF,CAAC,GAAG,CAAC,GAAG;UAAE,GAAGL,IAAI;UAAEpB,MAAM,EAAE;QAAU,CAAC,GAAGoB,IACrD,CAAC,CAAC;QACF,MAAM,IAAIQ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;MAC1D;IACF;IAEAnD,iBAAiB,CAAC,WAAW,CAAC;IAC9BI,aAAa,CAAC,IAAI,CAAC;IACnBN,YAAY,CAAC,KAAK,CAAC;;IAEnB;IACA,IAAIW,SAAS,EAAE;MACb,MAAMoB,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;MACtB,MAAM2B,cAAc,GAAGxB,IAAI,CAACC,KAAK,CAAC,CAACJ,GAAG,GAAGpB,SAAS,IAAI,IAAI,CAAC;MAC3DG,cAAc,CAACiC,IAAI,IAAIA,IAAI,GAAGW,cAAc,CAAC;MAC7C9C,YAAY,CAAC,IAAI,CAAC;IACpB;EACF,CAAC;;EAED;EACA,MAAM+C,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAItD,UAAU,KAAK,IAAI,EAAE;MACvB;MACAuB,QAAQ,CAACmB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KACpCA,KAAK,KAAK9C,UAAU,GAAG;QAAE,GAAGuC,IAAI;QAAEpB,MAAM,EAAE;MAAU,CAAC,GAAGoB,IAC1D,CAAC,CAAC;MACFC,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMe,aAAa,GAAGA,CAAA,KAAM;IAC1B5D,YAAY,CAAC,KAAK,CAAC;IACnBE,iBAAiB,CAAC,MAAM,CAAC;IACzBJ,cAAc,CAAC,CAAC,CAAC;IACjBM,eAAe,CAAC,CAAC,CAAC;IAClBE,aAAa,CAAC,IAAI,CAAC;IACnBI,wBAAwB,CAAC,CAAC,CAAC;IAC3BE,YAAY,CAAC,IAAI,CAAC;IAClBE,cAAc,CAAC,CAAC,CAAC;IACjB,MAAM+C,oBAAoB,GAAG7C,YAAY,GAAGU,uBAAuB,GAAGR,aAAa;IACnFU,QAAQ,CAACiC,oBAAoB,CAACb,GAAG,CAACJ,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEpB,MAAM,EAAE;IAAU,CAAC,CAAC,CAAC,CAAC;EAC9E,CAAC;;EAED;EACA,MAAMsC,QAAQ,GAAGA,CAAA,KAAM;IACrBtD,YAAY,CAAC,IAAI,CAAC;IAClB8C,UAAU,CAAC,MAAM9C,YAAY,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;EAC7C,CAAC;;EAED;EACA,MAAMuD,kBAAkB,GAAGA,CAAA,KAAM;IAC/B9C,eAAe,CAAC,IAAI,CAAC;IACrBW,QAAQ,CAACF,uBAAuB,CAACsB,GAAG,CAACJ,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEpB,MAAM,EAAE;IAAU,CAAC,CAAC,CAAC,CAAC;IAC/EtB,iBAAiB,CAAC,MAAM,CAAC;IACzBE,eAAe,CAAC,CAAC,CAAC;IAClBE,aAAa,CAAC,IAAI,CAAC;IACnBI,wBAAwB,CAAC,CAAC,CAAC;IAC3BE,YAAY,CAAC,IAAI,CAAC;IAClBE,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAMkD,mBAAmB,GAAGA,CAAA,KAAM;IAChC/C,eAAe,CAAC,KAAK,CAAC;IACtBW,QAAQ,CAACV,aAAa,CAAC8B,GAAG,CAACJ,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEpB,MAAM,EAAE;IAAU,CAAC,CAAC,CAAC,CAAC;IACrEtB,iBAAiB,CAAC,MAAM,CAAC;IACzBE,eAAe,CAAC,CAAC,CAAC;IAClBE,aAAa,CAAC,IAAI,CAAC;IACnBI,wBAAwB,CAAC,CAAC,CAAC;IAC3BE,YAAY,CAAC,IAAI,CAAC;IAClBE,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAMmD,eAAe,GAAGA,CAAA,KAAM;IAC5B7D,eAAe,CAAC,CAAC,CAAC;IAClBwB,QAAQ,CAACmB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KAAK;MACzC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO;QAAE,GAAGP,IAAI;QAAEpB,MAAM,EAAE;MAAY,CAAC;MACxD,IAAI2B,KAAK,KAAK,CAAC,EAAE,OAAO;QAAE,GAAGP,IAAI;QAAEpB,MAAM,EAAE;MAAY,CAAC;MACxD,IAAI2B,KAAK,KAAK,CAAC,EAAE,OAAO;QAAE,GAAGP,IAAI;QAAEpB,MAAM,EAAE;MAAS,CAAC;MACrD,OAAOoB,IAAI;IACb,CAAC,CAAC,CAAC;IACH1C,iBAAiB,CAAC,QAAQ,CAAC;IAC3BI,aAAa,CAAC,CAAC,CAAC;EAClB,CAAC;EAED,oBACEd,OAAA;IAAK0E,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBAErB3E,OAAA;MAAK0E,SAAS,EAAC,2FAA2F;MAAAC,QAAA,gBAExG3E,OAAA;QAAK0E,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzC3E,OAAA;UAAK0E,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C3E,OAAA;YAAK0E,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChF3E,OAAA;cAAM0E,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACN/E,OAAA;YAAA2E,QAAA,gBACE3E,OAAA;cAAI0E,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5C/E,OAAA;cAAG0E,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/E,OAAA;QAAK0E,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAElB3E,OAAA;UAAK0E,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB3E,OAAA;YAAK0E,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9C3E,OAAA;cAAG0E,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC/C/E,OAAA;cAAK0E,SAAS,EAAC,sDAAsD;cAAAC,QAAA,gBACnE3E,OAAA;gBAAM0E,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,2HAC5C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL,CAACtE,cAAc,KAAK,SAAS,IAAIA,cAAc,KAAK,WAAW,kBAC9DT,OAAA;UAAK0E,SAAS,EAAC,uDAAuD;UAAAC,QAAA,eACpE3E,OAAA;YAAK0E,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD3E,OAAA;cAAK0E,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C3E,OAAA;gBAAK0E,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,EAC/ElE,cAAc,KAAK,SAAS,gBAC3BT,OAAA,CAACH,OAAO;kBAAC6E,SAAS,EAAC;gBAAiC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEvD/E,OAAA,CAACN,WAAW;kBAACgF,SAAS,EAAC;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAC9C;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACN/E,OAAA;gBAAM0E,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,eACN/E,OAAA;cAAK0E,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpC3E,OAAA;gBAAM0E,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1C/E,OAAA;gBAAM0E,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE9B,UAAU,CAACxB,WAAW;cAAC;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGD/E,OAAA;UAAK0E,SAAS,EAAC,UAAU;UAAAC,QAAA,EACtBxC,KAAK,CAAC6C,KAAK,CAAC,CAAC,EAAErE,YAAY,CAAC,CAAC6C,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KAAK;YACjD,MAAMsB,QAAQ,GAAG9C,KAAK,CAACwB,KAAK,GAAG,CAAC,CAAC;YACjC,MAAMuB,mBAAmB,GAAGvB,KAAK,GAAGhD,YAAY,GAAG,CAAC,IAAIgD,KAAK,GAAGxB,KAAK,CAACuB,MAAM,GAAG,CAAC;YAChF,MAAMyB,cAAc,GAAG/B,IAAI,CAACpB,MAAM,KAAK,WAAW,GAAG,cAAc,GAAG,aAAa;YAEnF,oBACEhC,OAAA;cAAmB0E,SAAS,EAAC,UAAU;cAAAC,QAAA,GAEpCO,mBAAmB,iBAClBlF,OAAA;gBACE0E,SAAS,EAAE,+DAA+DS,cAAc,EAAG;gBAC3FC,KAAK,EAAE;kBACLC,MAAM,EAAE,MAAM,CAAC;gBACjB;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACP,eAGD/E,OAAA;gBAAK0E,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,gBAE5D3E,OAAA;kBAAK0E,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBAErC3E,OAAA;oBAAK0E,SAAS,EAAC;kBAA4E;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAGlG/E,OAAA;oBAAK0E,SAAS,EAAE,6GACdtB,IAAI,CAACpB,MAAM,KAAK,WAAW,GAAG,yBAAyB,GACvDoB,IAAI,CAACpB,MAAM,KAAK,QAAQ,GAAG,uBAAuB,GAClDoB,IAAI,CAACpB,MAAM,KAAK,SAAS,GAAG,wBAAwB,GACpDoB,IAAI,CAACpB,MAAM,KAAK,SAAS,GAAG,wBAAwB,GACpD,2BAA2B,EAC1B;oBAAA2C,QAAA,GACAvB,IAAI,CAACpB,MAAM,KAAK,WAAW,iBAAIhC,OAAA,CAACN,WAAW;sBAACgF,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAClE3B,IAAI,CAACpB,MAAM,KAAK,QAAQ,iBAAIhC,OAAA,CAACL,OAAO;sBAAC+E,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAC3D3B,IAAI,CAACpB,MAAM,KAAK,SAAS,iBAAIhC,OAAA,CAACH,OAAO;sBAAC6E,SAAS,EAAC;oBAAsB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACzE3B,IAAI,CAACpB,MAAM,KAAK,SAAS,iBAAIhC,OAAA,CAACH,OAAO;sBAAC6E,SAAS,EAAC;oBAAsB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGR/E,OAAA;kBAAK0E,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAClC3E,OAAA;oBAAK0E,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnB3E,OAAA;sBAAI0E,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEvB,IAAI,CAACxB;oBAAI;sBAAAgD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,EAGL3B,IAAI,CAACpB,MAAM,KAAK,QAAQ,iBACvBhC,OAAA;oBAAK0E,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EACnD,CAACvB,IAAI,CAACzB,EAAE,KAAK,QAAQ,IAAIyB,IAAI,CAACzB,EAAE,KAAK,oBAAoB,KAAKyB,IAAI,CAACpB,MAAM,KAAK,WAAW,gBACxFhC,OAAA;sBAAA2E,QAAA,EACGvB,IAAI,CAACzB,EAAE,KAAK,oBAAoB;sBAAA;sBAC/B;sBACA3B,OAAA;wBAAA2E,QAAA,gBACE3E,OAAA;0BAAK0E,SAAS,EAAC,kBAAkB;0BAAAC,QAAA,EAAC;wBAAQ;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAChD/E,OAAA;0BAAK0E,SAAS,EAAC,MAAM;0BAAAC,QAAA,EAAC;wBAAQ;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACpC/E,OAAA;0BAAK0E,SAAS,EAAC,MAAM;0BAAAC,QAAA,GAAC,gDAAW,eAAA3E,OAAA;4BAAM0E,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EAAC;0BAAK;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAClG/E,OAAA;0BAAK0E,SAAS,EAAC,MAAM;0BAAAC,QAAA,GAAC,gDAAW,eAAA3E,OAAA;4BAAM0E,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EAAC;0BAAK;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAClG/E,OAAA;0BAAK0E,SAAS,EAAC,MAAM;0BAAAC,QAAA,GAAC,sBAAK,eAAA3E,OAAA;4BAAM0E,SAAS,EAAC,6BAA6B;4BAAAC,QAAA,EAAC;0BAAK;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,4BAAI;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAE/F/E,OAAA;0BAAK0E,SAAS,EAAC,MAAM;0BAAAC,QAAA,EAAC;wBAAU;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAEtC/E,OAAA;0BAAK0E,SAAS,EAAC,MAAM;0BAAAC,QAAA,EAAC;wBAAU;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACtC/E,OAAA;0BAAK0E,SAAS,EAAC,MAAM;0BAAAC,QAAA,EAAC;wBAAa;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACzC/E,OAAA;0BAAK0E,SAAS,EAAC,gDAAgD;0BAAAC,QAAA,gBAC7D3E,OAAA;4BAAA2E,QAAA,EAAK;0BAAmB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC9B/E,OAAA;4BAAA2E,QAAA,EAAK;0BAAmB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3B,CAAC,eACN/E,OAAA;0BAAK0E,SAAS,EAAC,MAAM;0BAAAC,QAAA,EAAC;wBAAY;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACxC/E,OAAA;0BAAK0E,SAAS,EAAC,gDAAgD;0BAAAC,QAAA,gBAC7D3E,OAAA;4BAAA2E,QAAA,EAAK;0BAAmB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC9B/E,OAAA;4BAAA2E,QAAA,EAAK;0BAAmB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3B,CAAC,eAEN/E,OAAA;0BAAK0E,SAAS,EAAC,MAAM;0BAAAC,QAAA,EAAC;wBAAU;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACtC/E,OAAA;0BAAK0E,SAAS,EAAC,MAAM;0BAAAC,QAAA,EAAC;wBAAa;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACzC/E,OAAA;0BAAK0E,SAAS,EAAC,gDAAgD;0BAAAC,QAAA,gBAC7D3E,OAAA;4BAAA2E,QAAA,EAAK;0BAAmB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC9B/E,OAAA;4BAAA2E,QAAA,EAAK;0BAAmB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3B,CAAC,eACN/E,OAAA;0BAAK0E,SAAS,EAAC,MAAM;0BAAAC,QAAA,EAAC;wBAAY;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACxC/E,OAAA;0BAAK0E,SAAS,EAAC,2CAA2C;0BAAAC,QAAA,gBACxD3E,OAAA;4BAAA2E,QAAA,EAAK;0BAAmB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC9B/E,OAAA;4BAAA2E,QAAA,EAAK;0BAAmB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;sBAAA;sBAEN;sBACA/E,OAAA;wBAAA2E,QAAA,gBACE3E,OAAA;0BAAK0E,SAAS,EAAC,kBAAkB;0BAAAC,QAAA,EAAC;wBAAQ;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAChD/E,OAAA;0BAAK0E,SAAS,EAAC,MAAM;0BAAAC,QAAA,GAAC,iCAAM,eAAA3E,OAAA;4BAAM0E,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EAAC;0BAAK;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAC7F/E,OAAA;0BAAK0E,SAAS,EAAC,MAAM;0BAAAC,QAAA,GAAC,eAAG,eAAA3E,OAAA;4BAAM0E,SAAS,EAAC,+BAA+B;4BAAAC,QAAA,EAAC;0BAAI;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,iEAAW;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACrG/E,OAAA;0BAAK0E,SAAS,EAAC,2CAA2C;0BAAAC,QAAA,gBACxD3E,OAAA;4BAAA2E,QAAA,GAAK,SAAE,eAAA3E,OAAA;8BAAM0E,SAAS,EAAC,aAAa;8BAAAC,QAAA,EAAC;4BAAK;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,4HAAyB;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAChF/E,OAAA;4BAAA2E,QAAA,GAAK,SAAE,eAAA3E,OAAA;8BAAM0E,SAAS,EAAC,aAAa;8BAAAC,QAAA,EAAC;4BAAK;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,oGAAgB;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBACN;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,gBAEN/E,OAAA;sBAAA2E,QAAA,EAAIxB,kBAAkB,CAACC,IAAI;oBAAC;sBAAAwB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBACjC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CACN,EAGA3B,IAAI,CAACpB,MAAM,KAAK,QAAQ,iBACrBhC,OAAA;oBAAG0E,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAChCxB,kBAAkB,CAACC,IAAI;kBAAC;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CACN,EAGA3B,IAAI,CAACpB,MAAM,KAAK,QAAQ,iBACvBhC,OAAA;oBAAK0E,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnB3E,OAAA;sBACEsF,OAAO,EAAEnB,eAAgB;sBACzBoB,QAAQ,EAAEhF,SAAU;sBACpBmE,SAAS,EAAC,gQAAgQ;sBAC1Qc,KAAK,EAAC,cAAI;sBAAAb,QAAA,eAEV3E,OAAA,CAACP,SAAS;wBAACiF,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CACN,EAGA3B,IAAI,CAACpB,MAAM,KAAK,WAAW,IAAIvB,cAAc,KAAK,WAAW,IAAIkD,KAAK,KAAKxB,KAAK,CAACuB,MAAM,GAAG,CAAC,iBAC1F1D,OAAA;oBAAK0E,SAAS,EAAC,yCAAyC;oBAAAC,QAAA,gBAEtD3E,OAAA;sBAAK0E,SAAS,EAAC,yEAAyE;sBAAAC,QAAA,eACtF3E,OAAA;wBAAG0E,SAAS,EAAC,0CAA0C;wBAAAC,QAAA,EAAC;sBAExD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC,eAGN/E,OAAA;sBACEsF,OAAO,EAAEhB,QAAS;sBAClBI,SAAS,EAAC,mFAAmF;sBAC7Fc,KAAK,EAAC,0BAAM;sBAAAb,QAAA,eAEZ3E,OAAA,CAACF,IAAI;wBAAC4E,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GA5II3B,IAAI,CAACzB,EAAE;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA6Id,CAAC;UAER,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN/E,OAAA;UAAK0E,SAAS,EAAC,oCAAoC;UAAAC,QAAA,gBACjD3E,OAAA;YAAK0E,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD3E,OAAA;cAAK0E,SAAS,EAAC,6BAA6B;cAAAC,QAAA,GACzClE,cAAc,KAAK,MAAM,iBACxBT,OAAA,CAAAE,SAAA;gBAAAyE,QAAA,gBACE3E,OAAA;kBACEsF,OAAO,EAAEjC,eAAgB;kBACzBkC,QAAQ,EAAEhF,SAAU;kBACpBmE,SAAS,EAAC,0SAA0S;kBAAAC,QAAA,gBAEpT3E,OAAA,CAACR,IAAI;oBAACkF,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5B/E,OAAA;oBAAA2E,QAAA,EAAOnD,YAAY,GAAG,SAAS,GAAG;kBAAM;oBAAAoD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,EACR,CAACvD,YAAY,iBACZxB,OAAA;kBACEsF,OAAO,EAAEf,kBAAmB;kBAC5BG,SAAS,EAAC,kQAAkQ;kBAAAC,QAAA,eAE5Q3E,OAAA;oBAAA2E,QAAA,EAAM;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CACT,EACAvD,YAAY,iBACXxB,OAAA;kBACEsF,OAAO,EAAEd,mBAAoB;kBAC7BE,SAAS,EAAC,0PAA0P;kBAAAC,QAAA,eAEpQ3E,OAAA;oBAAA2E,QAAA,EAAM;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CACT,eACD/E,OAAA;kBACEsF,OAAO,EAAEb,eAAgB;kBACzBC,SAAS,EAAC,kQAAkQ;kBAAAC,QAAA,gBAE5Q3E,OAAA,CAACJ,WAAW;oBAAC8E,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnC/E,OAAA;oBAAA2E,QAAA,EAAM;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA,eACT,CACH,EAEAtE,cAAc,KAAK,SAAS,iBAC3BT,OAAA;gBACEuF,QAAQ;gBACRb,SAAS,EAAC,+IAA+I;gBAAAC,QAAA,gBAEzJ3E,OAAA,CAACH,OAAO;kBAAC6E,SAAS,EAAC;gBAAsB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5C/E,OAAA;kBAAA2E,QAAA,EAAM;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CACT,EAEAtE,cAAc,KAAK,WAAW,iBAC7BT,OAAA,CAAAE,SAAA;gBAAAyE,QAAA,gBACE3E,OAAA;kBACEsF,OAAO,EAAElB,aAAc;kBACvBM,SAAS,EAAC,8PAA8P;kBAAAC,QAAA,gBAExQ3E,OAAA,CAACN,WAAW;oBAACgF,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnC/E,OAAA;oBAAA2E,QAAA,EAAM;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACT/E,OAAA;kBACEsF,OAAO,EAAEb,eAAgB;kBACzBC,SAAS,EAAC,kQAAkQ;kBAAAC,QAAA,gBAE5Q3E,OAAA,CAACJ,WAAW;oBAAC8E,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnC/E,OAAA;oBAAA2E,QAAA,EAAM;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA,eACT,CACH,EAEAtE,cAAc,KAAK,QAAQ,iBAC1BT,OAAA;gBAAK0E,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C3E,OAAA;kBACEsF,OAAO,EAAEnB,eAAgB;kBACzBoB,QAAQ,EAAEhF,SAAU;kBACpBmE,SAAS,EAAC,kTAAkT;kBAAAC,QAAA,gBAE5T3E,OAAA,CAACP,SAAS;oBAACiF,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACjC/E,OAAA;oBAAA2E,QAAA,EAAM;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACT/E,OAAA;kBACEsF,OAAO,EAAElB,aAAc;kBACvBM,SAAS,EAAC,8NAA8N;kBAAAC,QAAA,EACzO;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGE,CAAC,eAGN/E,OAAA;cACEsF,OAAO,EAAElB,aAAc;cACvBM,SAAS,EAAC,gHAAgH;cAC1Hc,KAAK,EAAC,0BAAM;cAAAb,QAAA,eAEZ3E,OAAA,CAACP,SAAS;gBAACiF,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAILtE,cAAc,KAAK,QAAQ,IAAII,UAAU,KAAK,IAAI,iBACjDb,OAAA;YAAK0E,SAAS,EAAC,kGAAkG;YAAAC,QAAA,eAC/G3E,OAAA;cAAK0E,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C3E,OAAA;gBAAK0E,SAAS,EAAC,+GAA+G;gBAAAC,QAAA,eAC5H3E,OAAA,CAACJ,WAAW;kBAAC8E,SAAS,EAAC;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACN/E,OAAA;gBAAA2E,QAAA,gBACE3E,OAAA;kBAAK0E,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAC;gBAE3D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN/E,OAAA;kBAAK0E,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,GAAC,SAClC,EAAC9D,UAAU,GAAG,CAAC,EAAC,6FACpB;gBAAA;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLhE,SAAS,iBACRf,OAAA;MAAK0E,SAAS,EAAC,2HAA2H;MAAAC,QAAA,gBACxI3E,OAAA,CAACN,WAAW;QAACgF,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnC/E,OAAA;QAAA2E,QAAA,EAAM;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC3E,EAAA,CAvoBID,gBAAgB;AAAAsF,EAAA,GAAhBtF,gBAAgB;AAyoBtB,eAAeA,gBAAgB;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}