{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowDemo.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Play, RefreshCw, CheckCircle, XCircle, AlertCircle, Loader2, Save } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TemuWorkflowDemo = () => {\n  _s();\n  const [currentStep, setCurrentStep] = useState(0);\n  const [isRunning, setIsRunning] = useState(false);\n  const [workflowStatus, setWorkflowStatus] = useState('idle'); // idle, running, completed, failed\n  const [visibleSteps, setVisibleSteps] = useState(0); // 控制显示的步骤数量\n  const [failedStep, setFailedStep] = useState(null); // 记录失败的步骤\n  const [showToast, setShowToast] = useState(false); // 控制toast显示\n\n  // 工作流步骤定义\n  const workflowSteps = [{\n    id: 'login',\n    name: '登录',\n    runningDescription: '正在验证登录信息...',\n    completedDescription: '已完成登录【Temu账号】名下的店铺1',\n    duration: 2000,\n    status: 'pending'\n  }, {\n    id: 'product_filter',\n    name: '商品筛选',\n    runningDescription: '正在筛选符合条件的商品...',\n    completedDescription: '已筛选出【Temu账号】名下的店铺1，普通流量加速特权≥$5的商品，共200件商品',\n    duration: 3000,\n    status: 'pending'\n  }, {\n    id: 'product_processing',\n    name: '商品加权',\n    runningDescription: '正在为商品设置流量加权...',\n    completedDescription: '已完成对筛选出的商品，设置30天的普通流量加速加权。处理商品200件商品',\n    duration: 4000,\n    status: 'pending'\n  }, {\n    id: 'result',\n    name: '结果',\n    runningDescription: '正在生成执行结果...',\n    completedDescription: '执行完成！共处理200个商品，成功加速186个，跳过14个，总费用$892.40',\n    duration: 1000,\n    status: 'pending'\n  }];\n  const [steps, setSteps] = useState(workflowSteps);\n\n  // 获取步骤描述\n  const getStepDescription = step => {\n    if (step.status === 'running') {\n      return step.runningDescription;\n    } else if (step.status === 'completed') {\n      return step.completedDescription;\n    } else if (step.status === 'failed') {\n      return `执行失败：${step.runningDescription}`;\n    }\n    return '等待执行...';\n  };\n\n  // 模拟工作流执行\n  const executeWorkflow = async () => {\n    setIsRunning(true);\n    setWorkflowStatus('running');\n    setFailedStep(null);\n\n    // 从当前失败步骤或第一步开始\n    const startStep = failedStep !== null ? failedStep : 0;\n    setCurrentStep(startStep);\n\n    // 如果是重新开始，重置步骤状态\n    if (failedStep === null) {\n      setVisibleSteps(0);\n      setSteps(prev => prev.map(step => ({\n        ...step,\n        status: 'pending'\n      })));\n    }\n    for (let i = startStep; i < steps.length; i++) {\n      setCurrentStep(i);\n\n      // 显示当前步骤（如果还未显示）\n      if (i + 1 > visibleSteps) {\n        setVisibleSteps(i + 1);\n        // 确保新显示的步骤处于pending状态\n        setSteps(prev => prev.map((step, index) => index === i ? {\n          ...step,\n          status: 'pending'\n        } : step));\n        await new Promise(resolve => setTimeout(resolve, 300)); // 短暂延迟显示步骤\n      }\n\n      // 设置当前步骤为运行中\n      setSteps(prev => prev.map((step, index) => index === i ? {\n        ...step,\n        status: 'running'\n      } : step));\n\n      // 等待步骤完成\n      await new Promise(resolve => setTimeout(resolve, steps[i].duration));\n\n      // 随机失败演示（15%概率，在第2或第3步）\n      const shouldFail = Math.random() < 0.15 && (i === 1 || i === 2);\n      if (shouldFail) {\n        setSteps(prev => prev.map((step, index) => index === i ? {\n          ...step,\n          status: 'failed'\n        } : step));\n        setWorkflowStatus('failed');\n        setFailedStep(i);\n        setIsRunning(false);\n        return;\n      }\n\n      // 设置步骤为完成\n      setSteps(prev => prev.map((step, index) => index === i ? {\n        ...step,\n        status: 'completed'\n      } : step));\n\n      // 完成一步后显示下一步（如果不是最后一步）\n      if (i < steps.length - 1) {\n        setVisibleSteps(i + 2);\n        // 确保下一步显示为pending状态\n        setSteps(prev => prev.map((step, index) => index === i + 1 ? {\n          ...step,\n          status: 'pending'\n        } : step));\n        await new Promise(resolve => setTimeout(resolve, 500)); // 延迟显示下一步\n      }\n    }\n    setWorkflowStatus('completed');\n    setFailedStep(null);\n    setIsRunning(false);\n  };\n\n  // 重试失败的步骤\n  const retryFromFailed = () => {\n    if (failedStep !== null) {\n      // 重置失败步骤的状态\n      setSteps(prev => prev.map((step, index) => index === failedStep ? {\n        ...step,\n        status: 'pending'\n      } : step));\n      executeWorkflow();\n    }\n  };\n\n  // 重置整个工作流\n  const resetWorkflow = () => {\n    setIsRunning(false);\n    setWorkflowStatus('idle');\n    setCurrentStep(0);\n    setVisibleSteps(0);\n    setFailedStep(null);\n    setSteps(workflowSteps.map(step => ({\n      ...step,\n      status: 'pending'\n    })));\n  };\n\n  // 保存计划\n  const savePlan = () => {\n    setShowToast(true);\n    setTimeout(() => setShowToast(false), 3000);\n  };\n\n  // 模拟失败状态\n  const simulateFailure = () => {\n    setVisibleSteps(3);\n    setSteps(prev => prev.map((step, index) => {\n      if (index === 0) return {\n        ...step,\n        status: 'completed'\n      };\n      if (index === 1) return {\n        ...step,\n        status: 'completed'\n      };\n      if (index === 2) return {\n        ...step,\n        status: 'failed'\n      };\n      return step;\n    }));\n    setWorkflowStatus('failed');\n    setFailedStep(2);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg overflow-hidden border border-white/20\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-blue-500 text-white p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-bold\",\n              children: \"AI\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold\",\n              children: \"Temu\\u81EA\\u52A8\\u5316\\u52A9\\u624B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-100 text-sm\",\n              children: \"\\u6D41\\u91CF\\u52A0\\u901F\\u81EA\\u52A8\\u5316\\u6267\\u884C\\u4E2D...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-100 rounded-lg p-4 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-800 mb-2\",\n              children: \"\\u597D\\u7684\\uFF0C\\u5F00\\u59CB\\u6267\\u884C\\u8BA1\\u5212\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600 bg-white rounded-lg p-3 border\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: \"\\u6267\\u884C\\u914D\\u7F6E\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 17\n              }, this), \"\\u9AD8\\u7EA7\\u6D41\\u91CF\\u52A0\\u6743\\u6863\\u4F4D\\uFF0C\\u4EF7\\u683C\\u8303\\u56F44-6\\u7F8E\\u5143\\uFF0C\\u65F6\\u654830\\u5929\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: steps.slice(0, visibleSteps).map((step, index) => {\n            const nextStep = steps[index + 1];\n            const shouldShowConnector = index < visibleSteps - 1 && index < steps.length - 1;\n            const connectorColor = step.status === 'completed' ? 'bg-green-500' : 'bg-gray-300';\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [shouldShowConnector && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `absolute left-4 top-8 w-0.5 transition-all duration-500 z-0 ${connectorColor}`,\n                style: {\n                  height: '3rem' // 固定高度，只连接到下一个步骤的起始位置\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-4 relative z-10 pb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0 relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-white rounded-full absolute inset-0 z-10 border border-gray-100\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 relative z-20 shadow-sm ${step.status === 'completed' ? 'bg-green-500 text-white' : step.status === 'failed' ? 'bg-red-500 text-white' : step.status === 'running' ? 'bg-blue-500 text-white' : step.status === 'pending' ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600'}`,\n                    children: [step.status === 'completed' && /*#__PURE__*/_jsxDEV(CheckCircle, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 236,\n                      columnNumber: 57\n                    }, this), step.status === 'failed' && /*#__PURE__*/_jsxDEV(XCircle, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 237,\n                      columnNumber: 54\n                    }, this), step.status === 'running' && /*#__PURE__*/_jsxDEV(Loader2, {\n                      className: \"w-5 h-5 animate-spin\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 238,\n                      columnNumber: 55\n                    }, this), step.status === 'pending' && /*#__PURE__*/_jsxDEV(Loader2, {\n                      className: \"w-5 h-5 animate-spin\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 239,\n                      columnNumber: 55\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0 pt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-lg font-medium text-gray-900\",\n                      children: step.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 246,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 21\n                  }, this), step.status !== 'failed' && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600 leading-relaxed\",\n                    children: getStepDescription(step)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 23\n                  }, this), step.status === 'failed' && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-red-600 text-sm\",\n                    children: getStepDescription(step)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 25\n                  }, this), step.status === 'failed' && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: retryFromFailed,\n                      disabled: isRunning,\n                      className: \"p-1.5 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-full transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 active:translate-y-0\",\n                      title: \"\\u91CD\\u8BD5\",\n                      children: /*#__PURE__*/_jsxDEV(RefreshCw, {\n                        className: \"w-3 h-3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 272,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 266,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 23\n                  }, this), step.status === 'completed' && workflowStatus === 'completed' && index === steps.length - 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: savePlan,\n                        className: \"p-1.5 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-full transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 active:translate-y-0\",\n                        title: \"\\u4FDD\\u5B58\\u8BA1\\u5212\",\n                        children: /*#__PURE__*/_jsxDEV(Save, {\n                          className: \"w-3 h-3\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 287,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 282,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 281,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"inline-block bg-green-50 border border-green-200 rounded-full px-4 py-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-green-700 text-sm whitespace-nowrap\",\n                        children: \"\\u5DF2\\u5B8C\\u6210\\u5F53\\u524D\\u6240\\u6709\\u8BA1\\u5212\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 293,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 292,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 19\n              }, this)]\n            }, step.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-8 pt-4 border-t border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [workflowStatus === 'idle' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: executeWorkflow,\n                  disabled: isRunning,\n                  className: \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Play, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u5F00\\u59CB\\u6267\\u884C\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 318,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: simulateFailure,\n                  className: \"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u6A21\\u62DF\\u5931\\u8D25\\u72B6\\u6001\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true), workflowStatus === 'running' && /*#__PURE__*/_jsxDEV(\"button\", {\n                disabled: true,\n                className: \"bg-gradient-to-r from-gray-400 to-gray-500 text-white px-6 py-3 rounded-xl text-sm font-medium cursor-not-allowed flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(Loader2, {\n                  className: \"w-4 h-4 animate-spin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u6267\\u884C\\u4E2D...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 19\n              }, this), workflowStatus === 'completed' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: resetWorkflow,\n                  className: \"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 346,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u6267\\u884C\\u5B8C\\u6210\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: simulateFailure,\n                  className: \"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 353,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u6A21\\u62DF\\u5931\\u8D25\\u72B6\\u6001\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true), workflowStatus === 'failed' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: retryFromFailed,\n                  disabled: isRunning,\n                  className: \"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u91CD\\u8BD5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: resetWorkflow,\n                  className: \"bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0\",\n                  children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: resetWorkflow,\n              className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:shadow-sm\",\n              title: \"\\u91CD\\u65B0\\u5F00\\u59CB\",\n              children: /*#__PURE__*/_jsxDEV(RefreshCw, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this), workflowStatus === 'failed' && failedStep !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200/50 rounded-xl p-6 shadow-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg\",\n                children: /*#__PURE__*/_jsxDEV(AlertCircle, {\n                  className: \"w-5 h-5 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-red-700 font-semibold text-base mb-1\",\n                  children: \"\\u6267\\u884C\\u4E2D\\u65AD\\uFF0C\\u9700\\u8981\\u5904\\u7406\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-red-600 text-sm\",\n                  children: [\"\\u7B2C \", failedStep + 1, \" \\u6B65\\u51FA\\u73B0\\u95EE\\u9898\\uFF0C\\u53EF\\u70B9\\u51FB\\u91CD\\u8BD5\\u7EE7\\u7EED\\u6267\\u884C\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }, this), showToast && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-4 right-4 z-50 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg flex items-center space-x-2 animate-pulse\",\n      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n        className: \"w-5 h-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"\\u4FDD\\u5B58\\u8BA1\\u5212\\u6210\\u529F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 418,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 416,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 174,\n    columnNumber: 5\n  }, this);\n};\n_s(TemuWorkflowDemo, \"PDuFxBoIoC7+2NQuKr6VekNxTVI=\");\n_c = TemuWorkflowDemo;\nexport default TemuWorkflowDemo;\nvar _c;\n$RefreshReg$(_c, \"TemuWorkflowDemo\");", "map": {"version": 3, "names": ["React", "useState", "Play", "RefreshCw", "CheckCircle", "XCircle", "AlertCircle", "Loader2", "Save", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TemuWorkflowDemo", "_s", "currentStep", "setCurrentStep", "isRunning", "setIsRunning", "workflowStatus", "setWorkflowStatus", "visibleSteps", "setVisibleSteps", "failedStep", "setFailedStep", "showToast", "setShowToast", "workflowSteps", "id", "name", "runningDescription", "completedDescription", "duration", "status", "steps", "setSteps", "getStepDescription", "step", "executeWorkflow", "startStep", "prev", "map", "i", "length", "index", "Promise", "resolve", "setTimeout", "shouldFail", "Math", "random", "retryFromFailed", "resetWorkflow", "savePlan", "simulateFailure", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "slice", "nextStep", "shouldShowConnector", "connectorColor", "style", "height", "onClick", "disabled", "title", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowDemo.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Play, RefreshCw, CheckCircle, XCircle, AlertCircle, Loader2, Save } from 'lucide-react';\n\nconst TemuWorkflowDemo = () => {\n  const [currentStep, setCurrentStep] = useState(0);\n  const [isRunning, setIsRunning] = useState(false);\n  const [workflowStatus, setWorkflowStatus] = useState('idle'); // idle, running, completed, failed\n  const [visibleSteps, setVisibleSteps] = useState(0); // 控制显示的步骤数量\n  const [failedStep, setFailedStep] = useState(null); // 记录失败的步骤\n  const [showToast, setShowToast] = useState(false); // 控制toast显示\n\n  // 工作流步骤定义\n  const workflowSteps = [\n    {\n      id: 'login',\n      name: '登录',\n      runningDescription: '正在验证登录信息...',\n      completedDescription: '已完成登录【Temu账号】名下的店铺1',\n      duration: 2000,\n      status: 'pending'\n    },\n    {\n      id: 'product_filter',\n      name: '商品筛选', \n      runningDescription: '正在筛选符合条件的商品...',\n      completedDescription: '已筛选出【Temu账号】名下的店铺1，普通流量加速特权≥$5的商品，共200件商品',\n      duration: 3000,\n      status: 'pending'\n    },\n    {\n      id: 'product_processing',\n      name: '商品加权',\n      runningDescription: '正在为商品设置流量加权...',\n      completedDescription: '已完成对筛选出的商品，设置30天的普通流量加速加权。处理商品200件商品',\n      duration: 4000,\n      status: 'pending'\n    },\n    {\n      id: 'result',\n      name: '结果',\n      runningDescription: '正在生成执行结果...',\n      completedDescription: '执行完成！共处理200个商品，成功加速186个，跳过14个，总费用$892.40',\n      duration: 1000,\n      status: 'pending'\n    }\n  ];\n\n  const [steps, setSteps] = useState(workflowSteps);\n\n  // 获取步骤描述\n  const getStepDescription = (step) => {\n    if (step.status === 'running') {\n      return step.runningDescription;\n    } else if (step.status === 'completed') {\n      return step.completedDescription;\n    } else if (step.status === 'failed') {\n      return `执行失败：${step.runningDescription}`;\n    }\n    return '等待执行...';\n  };\n\n  // 模拟工作流执行\n  const executeWorkflow = async () => {\n    setIsRunning(true);\n    setWorkflowStatus('running');\n    setFailedStep(null);\n\n    // 从当前失败步骤或第一步开始\n    const startStep = failedStep !== null ? failedStep : 0;\n    setCurrentStep(startStep);\n\n    // 如果是重新开始，重置步骤状态\n    if (failedStep === null) {\n      setVisibleSteps(0);\n      setSteps(prev => prev.map(step => ({ ...step, status: 'pending' })));\n    }\n\n    for (let i = startStep; i < steps.length; i++) {\n      setCurrentStep(i);\n\n      // 显示当前步骤（如果还未显示）\n      if (i + 1 > visibleSteps) {\n        setVisibleSteps(i + 1);\n        // 确保新显示的步骤处于pending状态\n        setSteps(prev => prev.map((step, index) =>\n          index === i ? { ...step, status: 'pending' } : step\n        ));\n        await new Promise(resolve => setTimeout(resolve, 300)); // 短暂延迟显示步骤\n      }\n\n      // 设置当前步骤为运行中\n      setSteps(prev => prev.map((step, index) =>\n        index === i ? { ...step, status: 'running' } : step\n      ));\n\n      // 等待步骤完成\n      await new Promise(resolve => setTimeout(resolve, steps[i].duration));\n\n      // 随机失败演示（15%概率，在第2或第3步）\n      const shouldFail = Math.random() < 0.15 && (i === 1 || i === 2);\n\n      if (shouldFail) {\n        setSteps(prev => prev.map((step, index) =>\n          index === i ? { ...step, status: 'failed' } : step\n        ));\n        setWorkflowStatus('failed');\n        setFailedStep(i);\n        setIsRunning(false);\n        return;\n      }\n\n      // 设置步骤为完成\n      setSteps(prev => prev.map((step, index) =>\n        index === i ? { ...step, status: 'completed' } : step\n      ));\n\n      // 完成一步后显示下一步（如果不是最后一步）\n      if (i < steps.length - 1) {\n        setVisibleSteps(i + 2);\n        // 确保下一步显示为pending状态\n        setSteps(prev => prev.map((step, index) =>\n          index === i + 1 ? { ...step, status: 'pending' } : step\n        ));\n        await new Promise(resolve => setTimeout(resolve, 500)); // 延迟显示下一步\n      }\n    }\n\n    setWorkflowStatus('completed');\n    setFailedStep(null);\n    setIsRunning(false);\n  };\n\n  // 重试失败的步骤\n  const retryFromFailed = () => {\n    if (failedStep !== null) {\n      // 重置失败步骤的状态\n      setSteps(prev => prev.map((step, index) => \n        index === failedStep ? { ...step, status: 'pending' } : step\n      ));\n      executeWorkflow();\n    }\n  };\n\n  // 重置整个工作流\n  const resetWorkflow = () => {\n    setIsRunning(false);\n    setWorkflowStatus('idle');\n    setCurrentStep(0);\n    setVisibleSteps(0);\n    setFailedStep(null);\n    setSteps(workflowSteps.map(step => ({ ...step, status: 'pending' })));\n  };\n\n  // 保存计划\n  const savePlan = () => {\n    setShowToast(true);\n    setTimeout(() => setShowToast(false), 3000);\n  };\n\n  // 模拟失败状态\n  const simulateFailure = () => {\n    setVisibleSteps(3);\n    setSteps(prev => prev.map((step, index) => {\n      if (index === 0) return { ...step, status: 'completed' };\n      if (index === 1) return { ...step, status: 'completed' };\n      if (index === 2) return { ...step, status: 'failed' };\n      return step;\n    }));\n    setWorkflowStatus('failed');\n    setFailedStep(2);\n  };\n\n  return (\n    <div className=\"w-full\">\n      {/* AI对话框容器 */}\n      <div className=\"bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg overflow-hidden border border-white/20\">\n        {/* 对话框头部 */}\n        <div className=\"bg-blue-500 text-white p-4\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\">\n              <span className=\"text-sm font-bold\">AI</span>\n            </div>\n            <div>\n              <h3 className=\"font-semibold\">Temu自动化助手</h3>\n              <p className=\"text-blue-100 text-sm\">流量加速自动化执行中...</p>\n            </div>\n          </div>\n        </div>\n\n        {/* 对话内容 */}\n        <div className=\"p-6\">\n          {/* AI消息 */}\n          <div className=\"mb-6\">\n            <div className=\"bg-gray-100 rounded-lg p-4 mb-4\">\n              <p className=\"text-gray-800 mb-2\">好的，开始执行计划</p>\n              <div className=\"text-sm text-gray-600 bg-white rounded-lg p-3 border\">\n                <span className=\"font-medium\">执行配置：</span>高级流量加权档位，价格范围4-6美元，时效30天\n              </div>\n            </div>\n          </div>\n\n          {/* 优化后的时间线步骤列表 - 完美连接线 */}\n          <div className=\"relative\">\n            {steps.slice(0, visibleSteps).map((step, index) => {\n              const nextStep = steps[index + 1];\n              const shouldShowConnector = index < visibleSteps - 1 && index < steps.length - 1;\n              const connectorColor = step.status === 'completed' ? 'bg-green-500' : 'bg-gray-300';\n\n              return (\n                <div key={step.id} className=\"relative\">\n                  {/* 连接线 - 只在步骤完成后显示绿色，否则显示灰色 */}\n                  {shouldShowConnector && (\n                    <div\n                      className={`absolute left-4 top-8 w-0.5 transition-all duration-500 z-0 ${connectorColor}`}\n                      style={{\n                        height: '3rem' // 固定高度，只连接到下一个步骤的起始位置\n                      }}\n                    ></div>\n                  )}\n\n                  {/* 步骤内容容器 */}\n                  <div className=\"flex items-start space-x-4 relative z-10 pb-6\">\n                    {/* 状态图标容器 - 优化层级确保完美覆盖 */}\n                    <div className=\"flex-shrink-0 relative\">\n                      {/* 图标背景圆圈 - 确保完全覆盖连接线 */}\n                      <div className=\"w-8 h-8 bg-white rounded-full absolute inset-0 z-10 border border-gray-100\"></div>\n\n                      {/* 状态图标 - 最高层级 */}\n                      <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 relative z-20 shadow-sm ${\n                        step.status === 'completed' ? 'bg-green-500 text-white' :\n                        step.status === 'failed' ? 'bg-red-500 text-white' :\n                        step.status === 'running' ? 'bg-blue-500 text-white' :\n                        step.status === 'pending' ? 'bg-blue-500 text-white' :\n                        'bg-gray-300 text-gray-600'\n                      }`}>\n                        {step.status === 'completed' && <CheckCircle className=\"w-5 h-5\" />}\n                        {step.status === 'failed' && <XCircle className=\"w-5 h-5\" />}\n                        {step.status === 'running' && <Loader2 className=\"w-5 h-5 animate-spin\" />}\n                        {step.status === 'pending' && <Loader2 className=\"w-5 h-5 animate-spin\" />}\n                      </div>\n                    </div>\n\n                  {/* 步骤内容 */}\n                  <div className=\"flex-1 min-w-0 pt-1\">\n                    <div className=\"mb-2\">\n                      <h4 className=\"text-lg font-medium text-gray-900\">{step.name}</h4>\n                    </div>\n\n                    {/* 只在非失败状态时显示描述 */}\n                    {step.status !== 'failed' && (\n                      <p className=\"text-sm text-gray-600 leading-relaxed\">\n                        {getStepDescription(step)}\n                      </p>\n                    )}\n\n                    {/* 失败时的状态消息容器 */}\n                    {step.status === 'failed' && (\n                        <p className=\"text-red-600 text-sm\">\n                          {getStepDescription(step)}\n                        </p>\n                    )}\n\n                    {/* 重试按钮 - 在失败状态消息容器下方，与步骤标题左对齐 */}\n                    {step.status === 'failed' && (\n                      <div className=\"mt-3\">\n                        <button\n                          onClick={retryFromFailed}\n                          disabled={isRunning}\n                          className=\"p-1.5 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-full transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 active:translate-y-0\"\n                          title=\"重试\"\n                        >\n                          <RefreshCw className=\"w-3 h-3\" />\n                        </button>\n                      </div>\n                    )}\n\n                    {/* 成功状态：保存按钮和消息容器左对齐布局 */}\n                    {step.status === 'completed' && workflowStatus === 'completed' && index === steps.length - 1 && (\n                      <div className=\"mt-3\">\n                        {/* 保存按钮 - 左对齐 */}\n                        <div className=\"mb-2\">\n                          <button\n                            onClick={savePlan}\n                            className=\"p-1.5 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-full transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 active:translate-y-0\"\n                            title=\"保存计划\"\n                          >\n                            <Save className=\"w-3 h-3\" />\n                          </button>\n                        </div>\n\n                        {/* 成功状态消息容器 - 椭圆形态，宽度跟随文字，左对齐 */}\n                        <div className=\"inline-block bg-green-50 border border-green-200 rounded-full px-4 py-2\">\n                          <p className=\"text-green-700 text-sm whitespace-nowrap\">\n                           已完成当前所有计划\n                          </p>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            );\n            })}\n          </div>\n\n          {/* 操作按钮区域 */}\n          <div className=\"mt-8 pt-4 border-t border-gray-200\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                {workflowStatus === 'idle' && (\n                  <>\n                    <button\n                      onClick={executeWorkflow}\n                      disabled={isRunning}\n                      className=\"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                    >\n                      <Play className=\"w-4 h-4\" />\n                      <span>开始执行</span>\n                    </button>\n                    <button\n                      onClick={simulateFailure}\n                      className=\"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                    >\n                      <AlertCircle className=\"w-4 h-4\" />\n                      <span>模拟失败状态</span>\n                    </button>\n                  </>\n                )}\n\n                {workflowStatus === 'running' && (\n                  <button\n                    disabled\n                    className=\"bg-gradient-to-r from-gray-400 to-gray-500 text-white px-6 py-3 rounded-xl text-sm font-medium cursor-not-allowed flex items-center space-x-2\"\n                  >\n                    <Loader2 className=\"w-4 h-4 animate-spin\" />\n                    <span>执行中...</span>\n                  </button>\n                )}\n\n                {workflowStatus === 'completed' && (\n                  <>\n                    <button\n                      onClick={resetWorkflow}\n                      className=\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                    >\n                      <CheckCircle className=\"w-4 h-4\" />\n                      <span>执行完成</span>\n                    </button>\n                    <button\n                      onClick={simulateFailure}\n                      className=\"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                    >\n                      <AlertCircle className=\"w-4 h-4\" />\n                      <span>模拟失败状态</span>\n                    </button>\n                  </>\n                )}\n\n                {workflowStatus === 'failed' && (\n                  <div className=\"flex items-center space-x-3\">\n                    <button\n                      onClick={retryFromFailed}\n                      disabled={isRunning}\n                      className=\"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                    >\n                      <RefreshCw className=\"w-4 h-4\" />\n                      <span>重试</span>\n                    </button>\n                    <button\n                      onClick={resetWorkflow}\n                      className=\"bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0\"\n                    >\n                      重新开始\n                    </button>\n                  </div>\n                )}\n\n\n              </div>\n\n              {/* 刷新按钮 */}\n              <button\n                onClick={resetWorkflow}\n                className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:shadow-sm\"\n                title=\"重新开始\"\n              >\n                <RefreshCw className=\"w-4 h-4\" />\n              </button>\n            </div>\n\n   \n\n            {workflowStatus === 'failed' && failedStep !== null && (\n              <div className=\"mt-6 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200/50 rounded-xl p-6 shadow-sm\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"w-10 h-10 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg\">\n                    <AlertCircle className=\"w-5 h-5 text-white\" />\n                  </div>\n                  <div>\n                    <div className=\"text-red-700 font-semibold text-base mb-1\">\n                      执行中断，需要处理\n                    </div>\n                    <div className=\"text-red-600 text-sm\">\n                      第 {failedStep + 1} 步出现问题，可点击重试继续执行\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Toast 通知 */}\n      {showToast && (\n        <div className=\"fixed top-4 right-4 z-50 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg flex items-center space-x-2 animate-pulse\">\n          <CheckCircle className=\"w-5 h-5\" />\n          <span>保存计划成功</span>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TemuWorkflowDemo;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,WAAW,EAAEC,OAAO,EAAEC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjG,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkB,cAAc,EAAEC,iBAAiB,CAAC,GAAGnB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACrD,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACpD,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEnD;EACA,MAAM0B,aAAa,GAAG,CACpB;IACEC,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,IAAI;IACVC,kBAAkB,EAAE,aAAa;IACjCC,oBAAoB,EAAE,qBAAqB;IAC3CC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE;EACV,CAAC,EACD;IACEL,EAAE,EAAE,gBAAgB;IACpBC,IAAI,EAAE,MAAM;IACZC,kBAAkB,EAAE,gBAAgB;IACpCC,oBAAoB,EAAE,2CAA2C;IACjEC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE;EACV,CAAC,EACD;IACEL,EAAE,EAAE,oBAAoB;IACxBC,IAAI,EAAE,MAAM;IACZC,kBAAkB,EAAE,gBAAgB;IACpCC,oBAAoB,EAAE,sCAAsC;IAC5DC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE;EACV,CAAC,EACD;IACEL,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,IAAI;IACVC,kBAAkB,EAAE,aAAa;IACjCC,oBAAoB,EAAE,0CAA0C;IAChEC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGlC,QAAQ,CAAC0B,aAAa,CAAC;;EAEjD;EACA,MAAMS,kBAAkB,GAAIC,IAAI,IAAK;IACnC,IAAIA,IAAI,CAACJ,MAAM,KAAK,SAAS,EAAE;MAC7B,OAAOI,IAAI,CAACP,kBAAkB;IAChC,CAAC,MAAM,IAAIO,IAAI,CAACJ,MAAM,KAAK,WAAW,EAAE;MACtC,OAAOI,IAAI,CAACN,oBAAoB;IAClC,CAAC,MAAM,IAAIM,IAAI,CAACJ,MAAM,KAAK,QAAQ,EAAE;MACnC,OAAO,QAAQI,IAAI,CAACP,kBAAkB,EAAE;IAC1C;IACA,OAAO,SAAS;EAClB,CAAC;;EAED;EACA,MAAMQ,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCpB,YAAY,CAAC,IAAI,CAAC;IAClBE,iBAAiB,CAAC,SAAS,CAAC;IAC5BI,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,MAAMe,SAAS,GAAGhB,UAAU,KAAK,IAAI,GAAGA,UAAU,GAAG,CAAC;IACtDP,cAAc,CAACuB,SAAS,CAAC;;IAEzB;IACA,IAAIhB,UAAU,KAAK,IAAI,EAAE;MACvBD,eAAe,CAAC,CAAC,CAAC;MAClBa,QAAQ,CAACK,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACJ,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEJ,MAAM,EAAE;MAAU,CAAC,CAAC,CAAC,CAAC;IACtE;IAEA,KAAK,IAAIS,CAAC,GAAGH,SAAS,EAAEG,CAAC,GAAGR,KAAK,CAACS,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C1B,cAAc,CAAC0B,CAAC,CAAC;;MAEjB;MACA,IAAIA,CAAC,GAAG,CAAC,GAAGrB,YAAY,EAAE;QACxBC,eAAe,CAACoB,CAAC,GAAG,CAAC,CAAC;QACtB;QACAP,QAAQ,CAACK,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KACpCA,KAAK,KAAKF,CAAC,GAAG;UAAE,GAAGL,IAAI;UAAEJ,MAAM,EAAE;QAAU,CAAC,GAAGI,IACjD,CAAC,CAAC;QACF,MAAM,IAAIQ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;MAC1D;;MAEA;MACAX,QAAQ,CAACK,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KACpCA,KAAK,KAAKF,CAAC,GAAG;QAAE,GAAGL,IAAI;QAAEJ,MAAM,EAAE;MAAU,CAAC,GAAGI,IACjD,CAAC,CAAC;;MAEF;MACA,MAAM,IAAIQ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEZ,KAAK,CAACQ,CAAC,CAAC,CAACV,QAAQ,CAAC,CAAC;;MAEpE;MACA,MAAMgB,UAAU,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,KAAKR,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,CAAC;MAE/D,IAAIM,UAAU,EAAE;QACdb,QAAQ,CAACK,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KACpCA,KAAK,KAAKF,CAAC,GAAG;UAAE,GAAGL,IAAI;UAAEJ,MAAM,EAAE;QAAS,CAAC,GAAGI,IAChD,CAAC,CAAC;QACFjB,iBAAiB,CAAC,QAAQ,CAAC;QAC3BI,aAAa,CAACkB,CAAC,CAAC;QAChBxB,YAAY,CAAC,KAAK,CAAC;QACnB;MACF;;MAEA;MACAiB,QAAQ,CAACK,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KACpCA,KAAK,KAAKF,CAAC,GAAG;QAAE,GAAGL,IAAI;QAAEJ,MAAM,EAAE;MAAY,CAAC,GAAGI,IACnD,CAAC,CAAC;;MAEF;MACA,IAAIK,CAAC,GAAGR,KAAK,CAACS,MAAM,GAAG,CAAC,EAAE;QACxBrB,eAAe,CAACoB,CAAC,GAAG,CAAC,CAAC;QACtB;QACAP,QAAQ,CAACK,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KACpCA,KAAK,KAAKF,CAAC,GAAG,CAAC,GAAG;UAAE,GAAGL,IAAI;UAAEJ,MAAM,EAAE;QAAU,CAAC,GAAGI,IACrD,CAAC,CAAC;QACF,MAAM,IAAIQ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;MAC1D;IACF;IAEA1B,iBAAiB,CAAC,WAAW,CAAC;IAC9BI,aAAa,CAAC,IAAI,CAAC;IACnBN,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAMiC,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI5B,UAAU,KAAK,IAAI,EAAE;MACvB;MACAY,QAAQ,CAACK,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KACpCA,KAAK,KAAKrB,UAAU,GAAG;QAAE,GAAGc,IAAI;QAAEJ,MAAM,EAAE;MAAU,CAAC,GAAGI,IAC1D,CAAC,CAAC;MACFC,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMc,aAAa,GAAGA,CAAA,KAAM;IAC1BlC,YAAY,CAAC,KAAK,CAAC;IACnBE,iBAAiB,CAAC,MAAM,CAAC;IACzBJ,cAAc,CAAC,CAAC,CAAC;IACjBM,eAAe,CAAC,CAAC,CAAC;IAClBE,aAAa,CAAC,IAAI,CAAC;IACnBW,QAAQ,CAACR,aAAa,CAACc,GAAG,CAACJ,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEJ,MAAM,EAAE;IAAU,CAAC,CAAC,CAAC,CAAC;EACvE,CAAC;;EAED;EACA,MAAMoB,QAAQ,GAAGA,CAAA,KAAM;IACrB3B,YAAY,CAAC,IAAI,CAAC;IAClBqB,UAAU,CAAC,MAAMrB,YAAY,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;EAC7C,CAAC;;EAED;EACA,MAAM4B,eAAe,GAAGA,CAAA,KAAM;IAC5BhC,eAAe,CAAC,CAAC,CAAC;IAClBa,QAAQ,CAACK,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KAAK;MACzC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO;QAAE,GAAGP,IAAI;QAAEJ,MAAM,EAAE;MAAY,CAAC;MACxD,IAAIW,KAAK,KAAK,CAAC,EAAE,OAAO;QAAE,GAAGP,IAAI;QAAEJ,MAAM,EAAE;MAAY,CAAC;MACxD,IAAIW,KAAK,KAAK,CAAC,EAAE,OAAO;QAAE,GAAGP,IAAI;QAAEJ,MAAM,EAAE;MAAS,CAAC;MACrD,OAAOI,IAAI;IACb,CAAC,CAAC,CAAC;IACHjB,iBAAiB,CAAC,QAAQ,CAAC;IAC3BI,aAAa,CAAC,CAAC,CAAC;EAClB,CAAC;EAED,oBACEd,OAAA;IAAK6C,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBAErB9C,OAAA;MAAK6C,SAAS,EAAC,2FAA2F;MAAAC,QAAA,gBAExG9C,OAAA;QAAK6C,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzC9C,OAAA;UAAK6C,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C9C,OAAA;YAAK6C,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChF9C,OAAA;cAAM6C,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACNlD,OAAA;YAAA8C,QAAA,gBACE9C,OAAA;cAAI6C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5ClD,OAAA;cAAG6C,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlD,OAAA;QAAK6C,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAElB9C,OAAA;UAAK6C,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB9C,OAAA;YAAK6C,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9C9C,OAAA;cAAG6C,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC/ClD,OAAA;cAAK6C,SAAS,EAAC,sDAAsD;cAAAC,QAAA,gBACnE9C,OAAA;gBAAM6C,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,2HAC5C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlD,OAAA;UAAK6C,SAAS,EAAC,UAAU;UAAAC,QAAA,EACtBtB,KAAK,CAAC2B,KAAK,CAAC,CAAC,EAAExC,YAAY,CAAC,CAACoB,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KAAK;YACjD,MAAMkB,QAAQ,GAAG5B,KAAK,CAACU,KAAK,GAAG,CAAC,CAAC;YACjC,MAAMmB,mBAAmB,GAAGnB,KAAK,GAAGvB,YAAY,GAAG,CAAC,IAAIuB,KAAK,GAAGV,KAAK,CAACS,MAAM,GAAG,CAAC;YAChF,MAAMqB,cAAc,GAAG3B,IAAI,CAACJ,MAAM,KAAK,WAAW,GAAG,cAAc,GAAG,aAAa;YAEnF,oBACEvB,OAAA;cAAmB6C,SAAS,EAAC,UAAU;cAAAC,QAAA,GAEpCO,mBAAmB,iBAClBrD,OAAA;gBACE6C,SAAS,EAAE,+DAA+DS,cAAc,EAAG;gBAC3FC,KAAK,EAAE;kBACLC,MAAM,EAAE,MAAM,CAAC;gBACjB;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACP,eAGDlD,OAAA;gBAAK6C,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,gBAE5D9C,OAAA;kBAAK6C,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBAErC9C,OAAA;oBAAK6C,SAAS,EAAC;kBAA4E;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAGlGlD,OAAA;oBAAK6C,SAAS,EAAE,6GACdlB,IAAI,CAACJ,MAAM,KAAK,WAAW,GAAG,yBAAyB,GACvDI,IAAI,CAACJ,MAAM,KAAK,QAAQ,GAAG,uBAAuB,GAClDI,IAAI,CAACJ,MAAM,KAAK,SAAS,GAAG,wBAAwB,GACpDI,IAAI,CAACJ,MAAM,KAAK,SAAS,GAAG,wBAAwB,GACpD,2BAA2B,EAC1B;oBAAAuB,QAAA,GACAnB,IAAI,CAACJ,MAAM,KAAK,WAAW,iBAAIvB,OAAA,CAACN,WAAW;sBAACmD,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAClEvB,IAAI,CAACJ,MAAM,KAAK,QAAQ,iBAAIvB,OAAA,CAACL,OAAO;sBAACkD,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAC3DvB,IAAI,CAACJ,MAAM,KAAK,SAAS,iBAAIvB,OAAA,CAACH,OAAO;sBAACgD,SAAS,EAAC;oBAAsB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACzEvB,IAAI,CAACJ,MAAM,KAAK,SAAS,iBAAIvB,OAAA,CAACH,OAAO;sBAACgD,SAAS,EAAC;oBAAsB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGRlD,OAAA;kBAAK6C,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAClC9C,OAAA;oBAAK6C,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnB9C,OAAA;sBAAI6C,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEnB,IAAI,CAACR;oBAAI;sBAAA4B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,EAGLvB,IAAI,CAACJ,MAAM,KAAK,QAAQ,iBACvBvB,OAAA;oBAAG6C,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EACjDpB,kBAAkB,CAACC,IAAI;kBAAC;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CACJ,EAGAvB,IAAI,CAACJ,MAAM,KAAK,QAAQ,iBACrBvB,OAAA;oBAAG6C,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAChCpB,kBAAkB,CAACC,IAAI;kBAAC;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CACN,EAGAvB,IAAI,CAACJ,MAAM,KAAK,QAAQ,iBACvBvB,OAAA;oBAAK6C,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnB9C,OAAA;sBACEyD,OAAO,EAAEhB,eAAgB;sBACzBiB,QAAQ,EAAEnD,SAAU;sBACpBsC,SAAS,EAAC,gQAAgQ;sBAC1Qc,KAAK,EAAC,cAAI;sBAAAb,QAAA,eAEV9C,OAAA,CAACP,SAAS;wBAACoD,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CACN,EAGAvB,IAAI,CAACJ,MAAM,KAAK,WAAW,IAAId,cAAc,KAAK,WAAW,IAAIyB,KAAK,KAAKV,KAAK,CAACS,MAAM,GAAG,CAAC,iBAC1FjC,OAAA;oBAAK6C,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBAEnB9C,OAAA;sBAAK6C,SAAS,EAAC,MAAM;sBAAAC,QAAA,eACnB9C,OAAA;wBACEyD,OAAO,EAAEd,QAAS;wBAClBE,SAAS,EAAC,wMAAwM;wBAClNc,KAAK,EAAC,0BAAM;wBAAAb,QAAA,eAEZ9C,OAAA,CAACF,IAAI;0BAAC+C,SAAS,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eAGNlD,OAAA;sBAAK6C,SAAS,EAAC,yEAAyE;sBAAAC,QAAA,eACtF9C,OAAA;wBAAG6C,SAAS,EAAC,0CAA0C;wBAAAC,QAAA,EAAC;sBAExD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GA1FIvB,IAAI,CAACT,EAAE;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2Fd,CAAC;UAER,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNlD,OAAA;UAAK6C,SAAS,EAAC,oCAAoC;UAAAC,QAAA,gBACjD9C,OAAA;YAAK6C,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD9C,OAAA;cAAK6C,SAAS,EAAC,6BAA6B;cAAAC,QAAA,GACzCrC,cAAc,KAAK,MAAM,iBACxBT,OAAA,CAAAE,SAAA;gBAAA4C,QAAA,gBACE9C,OAAA;kBACEyD,OAAO,EAAE7B,eAAgB;kBACzB8B,QAAQ,EAAEnD,SAAU;kBACpBsC,SAAS,EAAC,0SAA0S;kBAAAC,QAAA,gBAEpT9C,OAAA,CAACR,IAAI;oBAACqD,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5BlD,OAAA;oBAAA8C,QAAA,EAAM;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACTlD,OAAA;kBACEyD,OAAO,EAAEb,eAAgB;kBACzBC,SAAS,EAAC,kQAAkQ;kBAAAC,QAAA,gBAE5Q9C,OAAA,CAACJ,WAAW;oBAACiD,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnClD,OAAA;oBAAA8C,QAAA,EAAM;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA,eACT,CACH,EAEAzC,cAAc,KAAK,SAAS,iBAC3BT,OAAA;gBACE0D,QAAQ;gBACRb,SAAS,EAAC,+IAA+I;gBAAAC,QAAA,gBAEzJ9C,OAAA,CAACH,OAAO;kBAACgD,SAAS,EAAC;gBAAsB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5ClD,OAAA;kBAAA8C,QAAA,EAAM;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CACT,EAEAzC,cAAc,KAAK,WAAW,iBAC7BT,OAAA,CAAAE,SAAA;gBAAA4C,QAAA,gBACE9C,OAAA;kBACEyD,OAAO,EAAEf,aAAc;kBACvBG,SAAS,EAAC,8PAA8P;kBAAAC,QAAA,gBAExQ9C,OAAA,CAACN,WAAW;oBAACmD,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnClD,OAAA;oBAAA8C,QAAA,EAAM;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACTlD,OAAA;kBACEyD,OAAO,EAAEb,eAAgB;kBACzBC,SAAS,EAAC,kQAAkQ;kBAAAC,QAAA,gBAE5Q9C,OAAA,CAACJ,WAAW;oBAACiD,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnClD,OAAA;oBAAA8C,QAAA,EAAM;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA,eACT,CACH,EAEAzC,cAAc,KAAK,QAAQ,iBAC1BT,OAAA;gBAAK6C,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C9C,OAAA;kBACEyD,OAAO,EAAEhB,eAAgB;kBACzBiB,QAAQ,EAAEnD,SAAU;kBACpBsC,SAAS,EAAC,kTAAkT;kBAAAC,QAAA,gBAE5T9C,OAAA,CAACP,SAAS;oBAACoD,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACjClD,OAAA;oBAAA8C,QAAA,EAAM;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACTlD,OAAA;kBACEyD,OAAO,EAAEf,aAAc;kBACvBG,SAAS,EAAC,8NAA8N;kBAAAC,QAAA,EACzO;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGE,CAAC,eAGNlD,OAAA;cACEyD,OAAO,EAAEf,aAAc;cACvBG,SAAS,EAAC,gHAAgH;cAC1Hc,KAAK,EAAC,0BAAM;cAAAb,QAAA,eAEZ9C,OAAA,CAACP,SAAS;gBAACoD,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAILzC,cAAc,KAAK,QAAQ,IAAII,UAAU,KAAK,IAAI,iBACjDb,OAAA;YAAK6C,SAAS,EAAC,kGAAkG;YAAAC,QAAA,eAC/G9C,OAAA;cAAK6C,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C9C,OAAA;gBAAK6C,SAAS,EAAC,+GAA+G;gBAAAC,QAAA,eAC5H9C,OAAA,CAACJ,WAAW;kBAACiD,SAAS,EAAC;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACNlD,OAAA;gBAAA8C,QAAA,gBACE9C,OAAA;kBAAK6C,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAC;gBAE3D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNlD,OAAA;kBAAK6C,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,GAAC,SAClC,EAACjC,UAAU,GAAG,CAAC,EAAC,6FACpB;gBAAA;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLnC,SAAS,iBACRf,OAAA;MAAK6C,SAAS,EAAC,2HAA2H;MAAAC,QAAA,gBACxI9C,OAAA,CAACN,WAAW;QAACmD,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnClD,OAAA;QAAA8C,QAAA,EAAM;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9C,EAAA,CAnaID,gBAAgB;AAAAyD,EAAA,GAAhBzD,gBAAgB;AAqatB,eAAeA,gBAAgB;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}