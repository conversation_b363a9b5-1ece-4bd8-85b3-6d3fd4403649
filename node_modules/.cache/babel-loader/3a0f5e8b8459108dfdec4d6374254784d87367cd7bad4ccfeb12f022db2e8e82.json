{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowShowcase.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Play, RefreshCw, CheckCircle, XCircle, Loader2, Zap, Target, Shield, Clock, ArrowRight, Star, Sparkles, TrendingUp, Users, Award } from 'lucide-react';\nimport TemuWorkflowDemo from './TemuWorkflowDemo';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TemuWorkflowShowcase = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('demo');\n  const features = [{\n    icon: /*#__PURE__*/_jsxDEV(Zap, {\n      className: \"w-6 h-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 13\n    }, this),\n    title: '智能自动化',\n    description: '全自动执行流量加速配置，无需人工干预',\n    color: 'from-blue-500 to-cyan-500'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(Target, {\n      className: \"w-6 h-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 13\n    }, this),\n    title: '精准筛选',\n    description: '智能筛选符合条件的商品，提高投放效率',\n    color: 'from-purple-500 to-pink-500'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(Shield, {\n      className: \"w-6 h-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 13\n    }, this),\n    title: '安全可靠',\n    description: '多重验证机制，确保操作安全性',\n    color: 'from-green-500 to-emerald-500'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(Clock, {\n      className: \"w-6 h-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 13\n    }, this),\n    title: '实时监控',\n    description: '实时显示执行进度，支持失败重试',\n    color: 'from-orange-500 to-red-500'\n  }];\n  const highlights = [{\n    icon: /*#__PURE__*/_jsxDEV(Sparkles, {\n      className: \"w-5 h-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 13\n    }, this),\n    text: '动态描述变化',\n    detail: '每个步骤都有详细的执行状态描述'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(TrendingUp, {\n      className: \"w-5 h-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 13\n    }, this),\n    text: '逐步显示时间线',\n    detail: '优雅的步骤展示动画效果'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(RefreshCw, {\n      className: \"w-5 h-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 13\n    }, this),\n    text: '智能失败重试',\n    detail: '自动检测失败并支持一键重试'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(Award, {\n      className: \"w-5 h-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 13\n    }, this),\n    text: '完美连接线',\n    detail: '精美的时间线视觉设计'\n  }];\n  const stats = [{\n    label: '处理商品',\n    value: '200+',\n    icon: /*#__PURE__*/_jsxDEV(Users, {\n      className: \"w-5 h-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 43\n    }, this)\n  }, {\n    label: '成功率',\n    value: '93%',\n    icon: /*#__PURE__*/_jsxDEV(TrendingUp, {\n      className: \"w-5 h-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 41\n    }, this)\n  }, {\n    label: '平均耗时',\n    value: '10s',\n    icon: /*#__PURE__*/_jsxDEV(Clock, {\n      className: \"w-5 h-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 42\n    }, this)\n  }, {\n    label: '节省时间',\n    value: '95%',\n    icon: /*#__PURE__*/_jsxDEV(Star, {\n      className: \"w-5 h-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 42\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-0 left-1/4 w-72 h-72 bg-blue-400/20 rounded-full blur-3xl\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-0 right-1/4 w-72 h-72 bg-purple-400/20 rounded-full blur-3xl\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-24\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n              className: \"w-4 h-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this), \"AI\\u9A71\\u52A8\\u7684\\u7535\\u5546\\u81EA\\u52A8\\u5316\\u89E3\\u51B3\\u65B9\\u6848\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-5xl md:text-6xl font-bold text-gray-900 mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n              children: \"Temu\\u6D41\\u91CF\\u52A0\\u901F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-800\",\n              children: \"\\u81EA\\u52A8\\u5316\\u5DE5\\u4F5C\\u6D41\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-3xl mx-auto mb-8 leading-relaxed\",\n            children: \"\\u4F53\\u9A8C\\u667A\\u80FD\\u5316\\u7684Temu\\u5546\\u54C1\\u6D41\\u91CF\\u52A0\\u901F\\u914D\\u7F6E\\u6D41\\u7A0B\\uFF0C\\u4ECE\\u767B\\u5F55\\u9A8C\\u8BC1\\u5230\\u5546\\u54C1\\u7B5B\\u9009\\uFF0C\\u518D\\u5230\\u6D41\\u91CF\\u52A0\\u6743\\u8BBE\\u7F6E\\uFF0C \\u5168\\u7A0B\\u81EA\\u52A8\\u5316\\u6267\\u884C\\uFF0C\\u8BA9\\u60A8\\u7684\\u7535\\u5546\\u8FD0\\u8425\\u66F4\\u52A0\\u9AD8\\u6548\\u4FBF\\u6377\\u3002\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto\",\n            children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-white/20\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-center mb-2 text-blue-600\",\n                children: stat.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: stat.value\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: stat.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16\",\n          children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"group\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 hover:-translate-y-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-12 h-12 rounded-xl bg-gradient-to-r ${feature.color} flex items-center justify-center text-white mb-4 group-hover:scale-110 transition-transform duration-300`,\n                children: feature.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                children: feature.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-sm leading-relaxed\",\n                children: feature.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 17\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/80 backdrop-blur-sm rounded-xl p-1 shadow-lg border border-white/20\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('demo'),\n              className: `px-6 py-3 rounded-lg font-medium transition-all duration-200 ${activeTab === 'demo' ? 'bg-blue-500 text-white shadow-lg' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'}`,\n              children: [/*#__PURE__*/_jsxDEV(Play, {\n                className: \"w-4 h-4 inline mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this), \"\\u5B9E\\u65F6\\u6F14\\u793A\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('features'),\n              className: `px-6 py-3 rounded-lg font-medium transition-all duration-200 ${activeTab === 'features' ? 'bg-blue-500 text-white shadow-lg' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'}`,\n              children: [/*#__PURE__*/_jsxDEV(Star, {\n                className: \"w-4 h-4 inline mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this), \"\\u529F\\u80FD\\u7279\\u6027\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this), activeTab === 'demo' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(TemuWorkflowDemo, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Target, {\n                className: \"w-5 h-5 mr-2 text-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 19\n              }, this), \"\\u64CD\\u4F5C\\u6307\\u5357\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5\",\n                  children: \"1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"\\u70B9\\u51FB\\\"\\u5F00\\u59CB\\u6267\\u884C\\\"\\u6309\\u94AE\\u542F\\u52A8\\u81EA\\u52A8\\u5316\\u6D41\\u7A0B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5\",\n                  children: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"\\u89C2\\u5BDF\\u6BCF\\u4E2A\\u6B65\\u9AA4\\u7684\\u5B9E\\u65F6\\u6267\\u884C\\u72B6\\u6001\\u548C\\u63CF\\u8FF0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5\",\n                  children: \"3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"\\u5982\\u9047\\u5931\\u8D25\\u53EF\\u70B9\\u51FB\\u91CD\\u8BD5\\u7EE7\\u7EED\\u6267\\u884C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5\",\n                  children: \"4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"\\u4F7F\\u7528Demo\\u63A7\\u5236\\u9762\\u677F\\u4F53\\u9A8C\\u4E0D\\u540C\\u573A\\u666F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n                className: \"w-5 h-5 mr-2 text-purple-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 19\n              }, this), \"\\u529F\\u80FD\\u4EAE\\u70B9\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: highlights.map((highlight, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-purple-500 mt-0.5\",\n                  children: highlight.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-medium text-gray-900 text-sm\",\n                    children: highlight.text\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-600 mt-1\",\n                    children: highlight.detail\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl p-6 text-white shadow-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold mb-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n                className: \"w-5 h-5 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 19\n              }, this), \"\\u9002\\u7528\\u573A\\u666F\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-2 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(ArrowRight, {\n                  className: \"w-4 h-4 mr-2 opacity-70\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 21\n                }, this), \"\\u5927\\u6279\\u91CF\\u5546\\u54C1\\u6D41\\u91CF\\u52A0\\u901F\\u914D\\u7F6E\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(ArrowRight, {\n                  className: \"w-4 h-4 mr-2 opacity-70\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 21\n                }, this), \"\\u5B9A\\u671F\\u8425\\u9500\\u6D3B\\u52A8\\u81EA\\u52A8\\u5316\\u6267\\u884C\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(ArrowRight, {\n                  className: \"w-4 h-4 mr-2 opacity-70\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 21\n                }, this), \"\\u591A\\u5E97\\u94FA\\u7EDF\\u4E00\\u8FD0\\u8425\\u7BA1\\u7406\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(ArrowRight, {\n                  className: \"w-4 h-4 mr-2 opacity-70\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 21\n                }, this), \"\\u7535\\u5546\\u8FD0\\u8425\\u6548\\u7387\\u63D0\\u5347\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 11\n      }, this), activeTab === 'features' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-gray-900 mb-8 text-center\",\n            children: \"\\u6838\\u5FC3\\u529F\\u80FD\\u8BE6\\u89E3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-semibold text-gray-900 mb-6 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Zap, {\n                  className: \"w-6 h-6 mr-3 text-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 21\n                }, this), \"\\u81EA\\u52A8\\u5316\\u5DE5\\u4F5C\\u6D41\\u7A0B\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid md:grid-cols-2 gap-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-50 rounded-xl p-4 border border-blue-100\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 flex-shrink-0\",\n                        children: \"1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 266,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-gray-900 leading-8\",\n                        children: \"\\u667A\\u80FD\\u767B\\u5F55\\u9A8C\\u8BC1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 267,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 265,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600 ml-11\",\n                      children: \"\\u81EA\\u52A8\\u9A8C\\u8BC1Temu\\u8D26\\u53F7\\u4FE1\\u606F\\uFF0C\\u786E\\u4FDD\\u5B89\\u5168\\u767B\\u5F55\\u5E76\\u83B7\\u53D6\\u5E97\\u94FA\\u6743\\u9650\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 269,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-purple-50 rounded-xl p-4 border border-purple-100\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 flex-shrink-0\",\n                        children: \"2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 274,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-gray-900 leading-8\",\n                        children: \"\\u7CBE\\u51C6\\u5546\\u54C1\\u7B5B\\u9009\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 275,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 273,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600 ml-11\",\n                      children: \"\\u6839\\u636E\\u8BBE\\u5B9A\\u6761\\u4EF6\\u667A\\u80FD\\u7B5B\\u9009\\u7B26\\u5408\\u8981\\u6C42\\u7684\\u5546\\u54C1\\uFF0C\\u63D0\\u9AD8\\u6295\\u653E\\u7CBE\\u51C6\\u5EA6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 277,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-green-50 rounded-xl p-4 border border-green-100\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 flex-shrink-0\",\n                        children: \"3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 284,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-gray-900 leading-8\",\n                        children: \"\\u6D41\\u91CF\\u52A0\\u6743\\u914D\\u7F6E\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 285,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 283,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600 ml-11\",\n                      children: \"\\u4E3A\\u7B5B\\u9009\\u51FA\\u7684\\u5546\\u54C1\\u81EA\\u52A8\\u8BBE\\u7F6E\\u6D41\\u91CF\\u52A0\\u901F\\u6743\\u91CD\\uFF0C\\u4F18\\u5316\\u66DD\\u5149\\u6548\\u679C\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 287,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-orange-50 rounded-xl p-4 border border-orange-100\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 flex-shrink-0\",\n                        children: \"4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 292,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-gray-900 leading-8\",\n                        children: \"\\u6267\\u884C\\u7ED3\\u679C\\u7EDF\\u8BA1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 293,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 291,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600 ml-11\",\n                      children: \"\\u751F\\u6210\\u8BE6\\u7EC6\\u7684\\u6267\\u884C\\u62A5\\u544A\\uFF0C\\u5305\\u542B\\u6210\\u529F\\u6570\\u91CF\\u3001\\u8D39\\u7528\\u7EDF\\u8BA1\\u7B49\\u4FE1\\u606F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 295,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 290,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border-t border-gray-200 pt-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-semibold text-gray-900 mb-6 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Shield, {\n                  className: \"w-6 h-6 mr-3 text-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 21\n                }, this), \"\\u6280\\u672F\\u7279\\u6027\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid md:grid-cols-3 gap-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-4\",\n                    children: /*#__PURE__*/_jsxDEV(Loader2, {\n                      className: \"w-8 h-8 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 311,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-semibold text-gray-900 mb-2\",\n                    children: \"\\u5B9E\\u65F6\\u72B6\\u6001\\u76D1\\u63A7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"\\u6BCF\\u4E2A\\u6B65\\u9AA4\\u90FD\\u6709\\u8BE6\\u7EC6\\u7684\\u72B6\\u6001\\u53CD\\u9988\\u548C\\u8FDB\\u5EA6\\u663E\\u793A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4\",\n                    children: /*#__PURE__*/_jsxDEV(RefreshCw, {\n                      className: \"w-8 h-8 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 319,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 318,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-semibold text-gray-900 mb-2\",\n                    children: \"\\u667A\\u80FD\\u9519\\u8BEF\\u6062\\u590D\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"\\u81EA\\u52A8\\u68C0\\u6D4B\\u6267\\u884C\\u5931\\u8D25\\u5E76\\u652F\\u6301\\u4E00\\u952E\\u91CD\\u8BD5\\u529F\\u80FD\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-4\",\n                    children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                      className: \"w-8 h-8 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 327,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 326,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-semibold text-gray-900 mb-2\",\n                    children: \"\\u4F18\\u96C5\\u7528\\u6237\\u4F53\\u9A8C\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"\\u6D41\\u7545\\u7684\\u52A8\\u753B\\u6548\\u679C\\u548C\\u76F4\\u89C2\\u7684\\u754C\\u9762\\u8BBE\\u8BA1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n};\n_s(TemuWorkflowShowcase, \"phYeN7kL3jpIZX53J650J8r3eaU=\");\n_c = TemuWorkflowShowcase;\nexport default TemuWorkflowShowcase;\nvar _c;\n$RefreshReg$(_c, \"TemuWorkflowShowcase\");", "map": {"version": 3, "names": ["React", "useState", "Play", "RefreshCw", "CheckCircle", "XCircle", "Loader2", "Zap", "Target", "Shield", "Clock", "ArrowRight", "Star", "<PERSON><PERSON><PERSON>", "TrendingUp", "Users", "Award", "TemuWorkflowDemo", "jsxDEV", "_jsxDEV", "TemuWorkflowShowcase", "_s", "activeTab", "setActiveTab", "features", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "description", "color", "highlights", "text", "detail", "stats", "label", "value", "children", "map", "stat", "index", "feature", "onClick", "highlight", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowShowcase.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { \n  Play, RefreshCw, CheckCircle, XCircle, Loader2, \n  Zap, Target, Shield, Clock, ArrowRight, Star,\n  Sparkles, TrendingUp, Users, Award\n} from 'lucide-react';\nimport TemuWorkflowDemo from './TemuWorkflowDemo';\n\nconst TemuWorkflowShowcase = () => {\n  const [activeTab, setActiveTab] = useState('demo');\n\n  const features = [\n    {\n      icon: <Zap className=\"w-6 h-6\" />,\n      title: '智能自动化',\n      description: '全自动执行流量加速配置，无需人工干预',\n      color: 'from-blue-500 to-cyan-500'\n    },\n    {\n      icon: <Target className=\"w-6 h-6\" />,\n      title: '精准筛选',\n      description: '智能筛选符合条件的商品，提高投放效率',\n      color: 'from-purple-500 to-pink-500'\n    },\n    {\n      icon: <Shield className=\"w-6 h-6\" />,\n      title: '安全可靠',\n      description: '多重验证机制，确保操作安全性',\n      color: 'from-green-500 to-emerald-500'\n    },\n    {\n      icon: <Clock className=\"w-6 h-6\" />,\n      title: '实时监控',\n      description: '实时显示执行进度，支持失败重试',\n      color: 'from-orange-500 to-red-500'\n    }\n  ];\n\n  const highlights = [\n    {\n      icon: <Sparkles className=\"w-5 h-5\" />,\n      text: '动态描述变化',\n      detail: '每个步骤都有详细的执行状态描述'\n    },\n    {\n      icon: <TrendingUp className=\"w-5 h-5\" />,\n      text: '逐步显示时间线',\n      detail: '优雅的步骤展示动画效果'\n    },\n    {\n      icon: <RefreshCw className=\"w-5 h-5\" />,\n      text: '智能失败重试',\n      detail: '自动检测失败并支持一键重试'\n    },\n    {\n      icon: <Award className=\"w-5 h-5\" />,\n      text: '完美连接线',\n      detail: '精美的时间线视觉设计'\n    }\n  ];\n\n  const stats = [\n    { label: '处理商品', value: '200+', icon: <Users className=\"w-5 h-5\" /> },\n    { label: '成功率', value: '93%', icon: <TrendingUp className=\"w-5 h-5\" /> },\n    { label: '平均耗时', value: '10s', icon: <Clock className=\"w-5 h-5\" /> },\n    { label: '节省时间', value: '95%', icon: <Star className=\"w-5 h-5\" /> }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\">\n      {/* 头部区域 */}\n      <div className=\"relative overflow-hidden\">\n        {/* 背景装饰 */}\n        <div className=\"absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10\"></div>\n        <div className=\"absolute top-0 left-1/4 w-72 h-72 bg-blue-400/20 rounded-full blur-3xl\"></div>\n        <div className=\"absolute top-0 right-1/4 w-72 h-72 bg-purple-400/20 rounded-full blur-3xl\"></div>\n        \n        <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-24\">\n          {/* 主标题区域 */}\n          <div className=\"text-center mb-16\">\n            <div className=\"inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-6\">\n              <Sparkles className=\"w-4 h-4 mr-2\" />\n              AI驱动的电商自动化解决方案\n            </div>\n            \n            <h1 className=\"text-5xl md:text-6xl font-bold text-gray-900 mb-6\">\n              <span className=\"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n                Temu流量加速\n              </span>\n              <br />\n              <span className=\"text-gray-800\">自动化工作流</span>\n            </h1>\n            \n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto mb-8 leading-relaxed\">\n              体验智能化的Temu商品流量加速配置流程，从登录验证到商品筛选，再到流量加权设置，\n              全程自动化执行，让您的电商运营更加高效便捷。\n            </p>\n\n            {/* 统计数据 */}\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto\">\n              {stats.map((stat, index) => (\n                <div key={index} className=\"bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-white/20\">\n                  <div className=\"flex items-center justify-center mb-2 text-blue-600\">\n                    {stat.icon}\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900\">{stat.value}</div>\n                  <div className=\"text-sm text-gray-600\">{stat.label}</div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* 功能特性卡片 */}\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16\">\n            {features.map((feature, index) => (\n              <div key={index} className=\"group\">\n                <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 hover:-translate-y-1\">\n                  <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${feature.color} flex items-center justify-center text-white mb-4 group-hover:scale-110 transition-transform duration-300`}>\n                    {feature.icon}\n                  </div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{feature.title}</h3>\n                  <p className=\"text-gray-600 text-sm leading-relaxed\">{feature.description}</p>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* 主要内容区域 */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16\">\n        {/* 标签页导航 */}\n        <div className=\"flex justify-center mb-8\">\n          <div className=\"bg-white/80 backdrop-blur-sm rounded-xl p-1 shadow-lg border border-white/20\">\n            <div className=\"flex space-x-1\">\n              <button\n                onClick={() => setActiveTab('demo')}\n                className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${\n                  activeTab === 'demo'\n                    ? 'bg-blue-500 text-white shadow-lg'\n                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                }`}\n              >\n                <Play className=\"w-4 h-4 inline mr-2\" />\n                实时演示\n              </button>\n              <button\n                onClick={() => setActiveTab('features')}\n                className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${\n                  activeTab === 'features'\n                    ? 'bg-blue-500 text-white shadow-lg'\n                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                }`}\n              >\n                <Star className=\"w-4 h-4 inline mr-2\" />\n                功能特性\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* 内容区域 */}\n        {activeTab === 'demo' && (\n          <div className=\"grid lg:grid-cols-3 gap-8\">\n            {/* 演示区域 */}\n            <div className=\"lg:col-span-2\">\n              <TemuWorkflowDemo />\n            </div>\n\n            {/* 侧边栏信息 */}\n            <div className=\"space-y-6\">\n              {/* 操作指南 */}\n              <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                  <Target className=\"w-5 h-5 mr-2 text-blue-500\" />\n                  操作指南\n                </h3>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-start space-x-3\">\n                    <div className=\"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5\">1</div>\n                    <p className=\"text-sm text-gray-600\">点击\"开始执行\"按钮启动自动化流程</p>\n                  </div>\n                  <div className=\"flex items-start space-x-3\">\n                    <div className=\"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5\">2</div>\n                    <p className=\"text-sm text-gray-600\">观察每个步骤的实时执行状态和描述</p>\n                  </div>\n                  <div className=\"flex items-start space-x-3\">\n                    <div className=\"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5\">3</div>\n                    <p className=\"text-sm text-gray-600\">如遇失败可点击重试继续执行</p>\n                  </div>\n                  <div className=\"flex items-start space-x-3\">\n                    <div className=\"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5\">4</div>\n                    <p className=\"text-sm text-gray-600\">使用Demo控制面板体验不同场景</p>\n                  </div>\n                </div>\n              </div>\n\n              {/* 功能亮点 */}\n              <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                  <Sparkles className=\"w-5 h-5 mr-2 text-purple-500\" />\n                  功能亮点\n                </h3>\n                <div className=\"space-y-4\">\n                  {highlights.map((highlight, index) => (\n                    <div key={index} className=\"flex items-start space-x-3\">\n                      <div className=\"text-purple-500 mt-0.5\">\n                        {highlight.icon}\n                      </div>\n                      <div>\n                        <div className=\"font-medium text-gray-900 text-sm\">{highlight.text}</div>\n                        <div className=\"text-xs text-gray-600 mt-1\">{highlight.detail}</div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* 使用场景 */}\n              <div className=\"bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl p-6 text-white shadow-lg\">\n                <h3 className=\"text-lg font-semibold mb-4 flex items-center\">\n                  <TrendingUp className=\"w-5 h-5 mr-2\" />\n                  适用场景\n                </h3>\n                <ul className=\"space-y-2 text-sm\">\n                  <li className=\"flex items-center\">\n                    <ArrowRight className=\"w-4 h-4 mr-2 opacity-70\" />\n                    大批量商品流量加速配置\n                  </li>\n                  <li className=\"flex items-center\">\n                    <ArrowRight className=\"w-4 h-4 mr-2 opacity-70\" />\n                    定期营销活动自动化执行\n                  </li>\n                  <li className=\"flex items-center\">\n                    <ArrowRight className=\"w-4 h-4 mr-2 opacity-70\" />\n                    多店铺统一运营管理\n                  </li>\n                  <li className=\"flex items-center\">\n                    <ArrowRight className=\"w-4 h-4 mr-2 opacity-70\" />\n                    电商运营效率提升\n                  </li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'features' && (\n          <div className=\"max-w-4xl mx-auto\">\n            {/* 详细功能介绍 */}\n            <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-8 text-center\">核心功能详解</h2>\n              \n              <div className=\"space-y-8\">\n                {/* 工作流步骤详解 */}\n                <div>\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-6 flex items-center\">\n                    <Zap className=\"w-6 h-6 mr-3 text-blue-500\" />\n                    自动化工作流程\n                  </h3>\n                  \n                  <div className=\"grid md:grid-cols-2 gap-6\">\n                    <div className=\"space-y-4\">\n                      <div className=\"bg-blue-50 rounded-xl p-4 border border-blue-100\">\n                        <div className=\"flex items-center mb-2\">\n                          <div className=\"w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 flex-shrink-0\">1</div>\n                          <h4 className=\"font-semibold text-gray-900 leading-8\">智能登录验证</h4>\n                        </div>\n                        <p className=\"text-sm text-gray-600 ml-11\">自动验证Temu账号信息，确保安全登录并获取店铺权限</p>\n                      </div>\n\n                      <div className=\"bg-purple-50 rounded-xl p-4 border border-purple-100\">\n                        <div className=\"flex items-center mb-2\">\n                          <div className=\"w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 flex-shrink-0\">2</div>\n                          <h4 className=\"font-semibold text-gray-900 leading-8\">精准商品筛选</h4>\n                        </div>\n                        <p className=\"text-sm text-gray-600 ml-11\">根据设定条件智能筛选符合要求的商品，提高投放精准度</p>\n                      </div>\n                    </div>\n                    \n                    <div className=\"space-y-4\">\n                      <div className=\"bg-green-50 rounded-xl p-4 border border-green-100\">\n                        <div className=\"flex items-center mb-2\">\n                          <div className=\"w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 flex-shrink-0\">3</div>\n                          <h4 className=\"font-semibold text-gray-900 leading-8\">流量加权配置</h4>\n                        </div>\n                        <p className=\"text-sm text-gray-600 ml-11\">为筛选出的商品自动设置流量加速权重，优化曝光效果</p>\n                      </div>\n\n                      <div className=\"bg-orange-50 rounded-xl p-4 border border-orange-100\">\n                        <div className=\"flex items-center mb-2\">\n                          <div className=\"w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 flex-shrink-0\">4</div>\n                          <h4 className=\"font-semibold text-gray-900 leading-8\">执行结果统计</h4>\n                        </div>\n                        <p className=\"text-sm text-gray-600 ml-11\">生成详细的执行报告，包含成功数量、费用统计等信息</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* 技术特性 */}\n                <div className=\"border-t border-gray-200 pt-8\">\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-6 flex items-center\">\n                    <Shield className=\"w-6 h-6 mr-3 text-green-500\" />\n                    技术特性\n                  </h3>\n                  \n                  <div className=\"grid md:grid-cols-3 gap-6\">\n                    <div className=\"text-center\">\n                      <div className=\"w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-4\">\n                        <Loader2 className=\"w-8 h-8 text-white\" />\n                      </div>\n                      <h4 className=\"font-semibold text-gray-900 mb-2\">实时状态监控</h4>\n                      <p className=\"text-sm text-gray-600\">每个步骤都有详细的状态反馈和进度显示</p>\n                    </div>\n                    \n                    <div className=\"text-center\">\n                      <div className=\"w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4\">\n                        <RefreshCw className=\"w-8 h-8 text-white\" />\n                      </div>\n                      <h4 className=\"font-semibold text-gray-900 mb-2\">智能错误恢复</h4>\n                      <p className=\"text-sm text-gray-600\">自动检测执行失败并支持一键重试功能</p>\n                    </div>\n                    \n                    <div className=\"text-center\">\n                      <div className=\"w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-4\">\n                        <CheckCircle className=\"w-8 h-8 text-white\" />\n                      </div>\n                      <h4 className=\"font-semibold text-gray-900 mb-2\">优雅用户体验</h4>\n                      <p className=\"text-sm text-gray-600\">流畅的动画效果和直观的界面设计</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default TemuWorkflowShowcase;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,IAAI,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,OAAO,EAC9CC,GAAG,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,EAAEC,IAAI,EAC5CC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,EAAEC,KAAK,QAC7B,cAAc;AACrB,OAAOC,gBAAgB,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,MAAM,CAAC;EAElD,MAAMuB,QAAQ,GAAG,CACf;IACEC,IAAI,eAAEN,OAAA,CAACZ,GAAG;MAACmB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjCC,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,oBAAoB;IACjCC,KAAK,EAAE;EACT,CAAC,EACD;IACER,IAAI,eAAEN,OAAA,CAACX,MAAM;MAACkB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpCC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,oBAAoB;IACjCC,KAAK,EAAE;EACT,CAAC,EACD;IACER,IAAI,eAAEN,OAAA,CAACV,MAAM;MAACiB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpCC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,gBAAgB;IAC7BC,KAAK,EAAE;EACT,CAAC,EACD;IACER,IAAI,eAAEN,OAAA,CAACT,KAAK;MAACgB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnCC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,iBAAiB;IAC9BC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,UAAU,GAAG,CACjB;IACET,IAAI,eAAEN,OAAA,CAACN,QAAQ;MAACa,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtCK,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE;EACV,CAAC,EACD;IACEX,IAAI,eAAEN,OAAA,CAACL,UAAU;MAACY,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxCK,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE;EACV,CAAC,EACD;IACEX,IAAI,eAAEN,OAAA,CAAChB,SAAS;MAACuB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvCK,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE;EACV,CAAC,EACD;IACEX,IAAI,eAAEN,OAAA,CAACH,KAAK;MAACU,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnCK,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAMC,KAAK,GAAG,CACZ;IAAEC,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE,MAAM;IAAEd,IAAI,eAAEN,OAAA,CAACJ,KAAK;MAACW,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACrE;IAAEQ,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE,KAAK;IAAEd,IAAI,eAAEN,OAAA,CAACL,UAAU;MAACY,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACxE;IAAEQ,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE,KAAK;IAAEd,IAAI,eAAEN,OAAA,CAACT,KAAK;MAACgB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACpE;IAAEQ,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE,KAAK;IAAEd,IAAI,eAAEN,OAAA,CAACP,IAAI;MAACc,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,CACpE;EAED,oBACEX,OAAA;IAAKO,SAAS,EAAC,wEAAwE;IAAAc,QAAA,gBAErFrB,OAAA;MAAKO,SAAS,EAAC,0BAA0B;MAAAc,QAAA,gBAEvCrB,OAAA;QAAKO,SAAS,EAAC;MAAqE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC3FX,OAAA;QAAKO,SAAS,EAAC;MAAwE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC9FX,OAAA;QAAKO,SAAS,EAAC;MAA2E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEjGX,OAAA;QAAKO,SAAS,EAAC,6DAA6D;QAAAc,QAAA,gBAE1ErB,OAAA;UAAKO,SAAS,EAAC,mBAAmB;UAAAc,QAAA,gBAChCrB,OAAA;YAAKO,SAAS,EAAC,oGAAoG;YAAAc,QAAA,gBACjHrB,OAAA,CAACN,QAAQ;cAACa,SAAS,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,8EAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAENX,OAAA;YAAIO,SAAS,EAAC,mDAAmD;YAAAc,QAAA,gBAC/DrB,OAAA;cAAMO,SAAS,EAAC,4EAA4E;cAAAc,QAAA,EAAC;YAE7F;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPX,OAAA;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNX,OAAA;cAAMO,SAAS,EAAC,eAAe;cAAAc,QAAA,EAAC;YAAM;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eAELX,OAAA;YAAGO,SAAS,EAAC,8DAA8D;YAAAc,QAAA,EAAC;UAG5E;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAGJX,OAAA;YAAKO,SAAS,EAAC,yDAAyD;YAAAc,QAAA,EACrEH,KAAK,CAACI,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrBxB,OAAA;cAAiBO,SAAS,EAAC,8EAA8E;cAAAc,QAAA,gBACvGrB,OAAA;gBAAKO,SAAS,EAAC,qDAAqD;gBAAAc,QAAA,EACjEE,IAAI,CAACjB;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACNX,OAAA;gBAAKO,SAAS,EAAC,kCAAkC;gBAAAc,QAAA,EAAEE,IAAI,CAACH;cAAK;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpEX,OAAA;gBAAKO,SAAS,EAAC,uBAAuB;gBAAAc,QAAA,EAAEE,IAAI,CAACJ;cAAK;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GALjDa,KAAK;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNX,OAAA;UAAKO,SAAS,EAAC,gDAAgD;UAAAc,QAAA,EAC5DhB,QAAQ,CAACiB,GAAG,CAAC,CAACG,OAAO,EAAED,KAAK,kBAC3BxB,OAAA;YAAiBO,SAAS,EAAC,OAAO;YAAAc,QAAA,eAChCrB,OAAA;cAAKO,SAAS,EAAC,gJAAgJ;cAAAc,QAAA,gBAC7JrB,OAAA;gBAAKO,SAAS,EAAE,yCAAyCkB,OAAO,CAACX,KAAK,2GAA4G;gBAAAO,QAAA,EAC/KI,OAAO,CAACnB;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNX,OAAA;gBAAIO,SAAS,EAAC,0CAA0C;gBAAAc,QAAA,EAAEI,OAAO,CAACb;cAAK;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7EX,OAAA;gBAAGO,SAAS,EAAC,uCAAuC;gBAAAc,QAAA,EAAEI,OAAO,CAACZ;cAAW;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E;UAAC,GAPEa,KAAK;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNX,OAAA;MAAKO,SAAS,EAAC,8CAA8C;MAAAc,QAAA,gBAE3DrB,OAAA;QAAKO,SAAS,EAAC,0BAA0B;QAAAc,QAAA,eACvCrB,OAAA;UAAKO,SAAS,EAAC,8EAA8E;UAAAc,QAAA,eAC3FrB,OAAA;YAAKO,SAAS,EAAC,gBAAgB;YAAAc,QAAA,gBAC7BrB,OAAA;cACE0B,OAAO,EAAEA,CAAA,KAAMtB,YAAY,CAAC,MAAM,CAAE;cACpCG,SAAS,EAAE,gEACTJ,SAAS,KAAK,MAAM,GAChB,kCAAkC,GAClC,oDAAoD,EACvD;cAAAkB,QAAA,gBAEHrB,OAAA,CAACjB,IAAI;gBAACwB,SAAS,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAE1C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTX,OAAA;cACE0B,OAAO,EAAEA,CAAA,KAAMtB,YAAY,CAAC,UAAU,CAAE;cACxCG,SAAS,EAAE,gEACTJ,SAAS,KAAK,UAAU,GACpB,kCAAkC,GAClC,oDAAoD,EACvD;cAAAkB,QAAA,gBAEHrB,OAAA,CAACP,IAAI;gBAACc,SAAS,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAE1C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLR,SAAS,KAAK,MAAM,iBACnBH,OAAA;QAAKO,SAAS,EAAC,2BAA2B;QAAAc,QAAA,gBAExCrB,OAAA;UAAKO,SAAS,EAAC,eAAe;UAAAc,QAAA,eAC5BrB,OAAA,CAACF,gBAAgB;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eAGNX,OAAA;UAAKO,SAAS,EAAC,WAAW;UAAAc,QAAA,gBAExBrB,OAAA;YAAKO,SAAS,EAAC,+EAA+E;YAAAc,QAAA,gBAC5FrB,OAAA;cAAIO,SAAS,EAAC,4DAA4D;cAAAc,QAAA,gBACxErB,OAAA,CAACX,MAAM;gBAACkB,SAAS,EAAC;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAEnD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLX,OAAA;cAAKO,SAAS,EAAC,WAAW;cAAAc,QAAA,gBACxBrB,OAAA;gBAAKO,SAAS,EAAC,4BAA4B;gBAAAc,QAAA,gBACzCrB,OAAA;kBAAKO,SAAS,EAAC,0HAA0H;kBAAAc,QAAA,EAAC;gBAAC;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjJX,OAAA;kBAAGO,SAAS,EAAC,uBAAuB;kBAAAc,QAAA,EAAC;gBAAiB;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACNX,OAAA;gBAAKO,SAAS,EAAC,4BAA4B;gBAAAc,QAAA,gBACzCrB,OAAA;kBAAKO,SAAS,EAAC,0HAA0H;kBAAAc,QAAA,EAAC;gBAAC;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjJX,OAAA;kBAAGO,SAAS,EAAC,uBAAuB;kBAAAc,QAAA,EAAC;gBAAgB;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACNX,OAAA;gBAAKO,SAAS,EAAC,4BAA4B;gBAAAc,QAAA,gBACzCrB,OAAA;kBAAKO,SAAS,EAAC,0HAA0H;kBAAAc,QAAA,EAAC;gBAAC;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjJX,OAAA;kBAAGO,SAAS,EAAC,uBAAuB;kBAAAc,QAAA,EAAC;gBAAa;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eACNX,OAAA;gBAAKO,SAAS,EAAC,4BAA4B;gBAAAc,QAAA,gBACzCrB,OAAA;kBAAKO,SAAS,EAAC,0HAA0H;kBAAAc,QAAA,EAAC;gBAAC;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjJX,OAAA;kBAAGO,SAAS,EAAC,uBAAuB;kBAAAc,QAAA,EAAC;gBAAgB;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNX,OAAA;YAAKO,SAAS,EAAC,+EAA+E;YAAAc,QAAA,gBAC5FrB,OAAA;cAAIO,SAAS,EAAC,4DAA4D;cAAAc,QAAA,gBACxErB,OAAA,CAACN,QAAQ;gBAACa,SAAS,EAAC;cAA8B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAEvD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLX,OAAA;cAAKO,SAAS,EAAC,WAAW;cAAAc,QAAA,EACvBN,UAAU,CAACO,GAAG,CAAC,CAACK,SAAS,EAAEH,KAAK,kBAC/BxB,OAAA;gBAAiBO,SAAS,EAAC,4BAA4B;gBAAAc,QAAA,gBACrDrB,OAAA;kBAAKO,SAAS,EAAC,wBAAwB;kBAAAc,QAAA,EACpCM,SAAS,CAACrB;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACNX,OAAA;kBAAAqB,QAAA,gBACErB,OAAA;oBAAKO,SAAS,EAAC,mCAAmC;oBAAAc,QAAA,EAAEM,SAAS,CAACX;kBAAI;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzEX,OAAA;oBAAKO,SAAS,EAAC,4BAA4B;oBAAAc,QAAA,EAAEM,SAAS,CAACV;kBAAM;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE,CAAC;cAAA,GAPEa,KAAK;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNX,OAAA;YAAKO,SAAS,EAAC,mFAAmF;YAAAc,QAAA,gBAChGrB,OAAA;cAAIO,SAAS,EAAC,8CAA8C;cAAAc,QAAA,gBAC1DrB,OAAA,CAACL,UAAU;gBAACY,SAAS,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAEzC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLX,OAAA;cAAIO,SAAS,EAAC,mBAAmB;cAAAc,QAAA,gBAC/BrB,OAAA;gBAAIO,SAAS,EAAC,mBAAmB;gBAAAc,QAAA,gBAC/BrB,OAAA,CAACR,UAAU;kBAACe,SAAS,EAAC;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,sEAEpD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLX,OAAA;gBAAIO,SAAS,EAAC,mBAAmB;gBAAAc,QAAA,gBAC/BrB,OAAA,CAACR,UAAU;kBAACe,SAAS,EAAC;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,sEAEpD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLX,OAAA;gBAAIO,SAAS,EAAC,mBAAmB;gBAAAc,QAAA,gBAC/BrB,OAAA,CAACR,UAAU;kBAACe,SAAS,EAAC;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,0DAEpD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLX,OAAA;gBAAIO,SAAS,EAAC,mBAAmB;gBAAAc,QAAA,gBAC/BrB,OAAA,CAACR,UAAU;kBAACe,SAAS,EAAC;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,oDAEpD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEAR,SAAS,KAAK,UAAU,iBACvBH,OAAA;QAAKO,SAAS,EAAC,mBAAmB;QAAAc,QAAA,eAEhCrB,OAAA;UAAKO,SAAS,EAAC,+EAA+E;UAAAc,QAAA,gBAC5FrB,OAAA;YAAIO,SAAS,EAAC,mDAAmD;YAAAc,QAAA,EAAC;UAAM;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE7EX,OAAA;YAAKO,SAAS,EAAC,WAAW;YAAAc,QAAA,gBAExBrB,OAAA;cAAAqB,QAAA,gBACErB,OAAA;gBAAIO,SAAS,EAAC,4DAA4D;gBAAAc,QAAA,gBACxErB,OAAA,CAACZ,GAAG;kBAACmB,SAAS,EAAC;gBAA4B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,8CAEhD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAELX,OAAA;gBAAKO,SAAS,EAAC,2BAA2B;gBAAAc,QAAA,gBACxCrB,OAAA;kBAAKO,SAAS,EAAC,WAAW;kBAAAc,QAAA,gBACxBrB,OAAA;oBAAKO,SAAS,EAAC,kDAAkD;oBAAAc,QAAA,gBAC/DrB,OAAA;sBAAKO,SAAS,EAAC,wBAAwB;sBAAAc,QAAA,gBACrCrB,OAAA;wBAAKO,SAAS,EAAC,mHAAmH;wBAAAc,QAAA,EAAC;sBAAC;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC1IX,OAAA;wBAAIO,SAAS,EAAC,uCAAuC;wBAAAc,QAAA,EAAC;sBAAM;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACNX,OAAA;sBAAGO,SAAS,EAAC,6BAA6B;sBAAAc,QAAA,EAAC;oBAA0B;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC,eAENX,OAAA;oBAAKO,SAAS,EAAC,sDAAsD;oBAAAc,QAAA,gBACnErB,OAAA;sBAAKO,SAAS,EAAC,wBAAwB;sBAAAc,QAAA,gBACrCrB,OAAA;wBAAKO,SAAS,EAAC,qHAAqH;wBAAAc,QAAA,EAAC;sBAAC;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC5IX,OAAA;wBAAIO,SAAS,EAAC,uCAAuC;wBAAAc,QAAA,EAAC;sBAAM;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACNX,OAAA;sBAAGO,SAAS,EAAC,6BAA6B;sBAAAc,QAAA,EAAC;oBAAyB;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENX,OAAA;kBAAKO,SAAS,EAAC,WAAW;kBAAAc,QAAA,gBACxBrB,OAAA;oBAAKO,SAAS,EAAC,oDAAoD;oBAAAc,QAAA,gBACjErB,OAAA;sBAAKO,SAAS,EAAC,wBAAwB;sBAAAc,QAAA,gBACrCrB,OAAA;wBAAKO,SAAS,EAAC,oHAAoH;wBAAAc,QAAA,EAAC;sBAAC;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC3IX,OAAA;wBAAIO,SAAS,EAAC,uCAAuC;wBAAAc,QAAA,EAAC;sBAAM;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACNX,OAAA;sBAAGO,SAAS,EAAC,6BAA6B;sBAAAc,QAAA,EAAC;oBAAwB;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC,eAENX,OAAA;oBAAKO,SAAS,EAAC,sDAAsD;oBAAAc,QAAA,gBACnErB,OAAA;sBAAKO,SAAS,EAAC,wBAAwB;sBAAAc,QAAA,gBACrCrB,OAAA;wBAAKO,SAAS,EAAC,qHAAqH;wBAAAc,QAAA,EAAC;sBAAC;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC5IX,OAAA;wBAAIO,SAAS,EAAC,uCAAuC;wBAAAc,QAAA,EAAC;sBAAM;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACNX,OAAA;sBAAGO,SAAS,EAAC,6BAA6B;sBAAAc,QAAA,EAAC;oBAAwB;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNX,OAAA;cAAKO,SAAS,EAAC,+BAA+B;cAAAc,QAAA,gBAC5CrB,OAAA;gBAAIO,SAAS,EAAC,4DAA4D;gBAAAc,QAAA,gBACxErB,OAAA,CAACV,MAAM;kBAACiB,SAAS,EAAC;gBAA6B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAEpD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAELX,OAAA;gBAAKO,SAAS,EAAC,2BAA2B;gBAAAc,QAAA,gBACxCrB,OAAA;kBAAKO,SAAS,EAAC,aAAa;kBAAAc,QAAA,gBAC1BrB,OAAA;oBAAKO,SAAS,EAAC,gHAAgH;oBAAAc,QAAA,eAC7HrB,OAAA,CAACb,OAAO;sBAACoB,SAAS,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,eACNX,OAAA;oBAAIO,SAAS,EAAC,kCAAkC;oBAAAc,QAAA,EAAC;kBAAM;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5DX,OAAA;oBAAGO,SAAS,EAAC,uBAAuB;oBAAAc,QAAA,EAAC;kBAAkB;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC,eAENX,OAAA;kBAAKO,SAAS,EAAC,aAAa;kBAAAc,QAAA,gBAC1BrB,OAAA;oBAAKO,SAAS,EAAC,kHAAkH;oBAAAc,QAAA,eAC/HrB,OAAA,CAAChB,SAAS;sBAACuB,SAAS,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC,eACNX,OAAA;oBAAIO,SAAS,EAAC,kCAAkC;oBAAAc,QAAA,EAAC;kBAAM;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5DX,OAAA;oBAAGO,SAAS,EAAC,uBAAuB;oBAAAc,QAAA,EAAC;kBAAiB;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,eAENX,OAAA;kBAAKO,SAAS,EAAC,aAAa;kBAAAc,QAAA,gBAC1BrB,OAAA;oBAAKO,SAAS,EAAC,oHAAoH;oBAAAc,QAAA,eACjIrB,OAAA,CAACf,WAAW;sBAACsB,SAAS,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC,eACNX,OAAA;oBAAIO,SAAS,EAAC,kCAAkC;oBAAAc,QAAA,EAAC;kBAAM;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5DX,OAAA;oBAAGO,SAAS,EAAC,uBAAuB;oBAAAc,QAAA,EAAC;kBAAe;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACT,EAAA,CA5UID,oBAAoB;AAAA2B,EAAA,GAApB3B,oBAAoB;AA8U1B,eAAeA,oBAAoB;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}