{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowDemo.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Play, RefreshCw, CheckCircle, XCircle, AlertCircle, Loader2, Save } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TemuWorkflowDemo = () => {\n  _s();\n  const [currentStep, setCurrentStep] = useState(0);\n  const [isRunning, setIsRunning] = useState(false);\n  const [workflowStatus, setWorkflowStatus] = useState('idle'); // idle, running, completed, failed\n  const [visibleSteps, setVisibleSteps] = useState(0); // 控制显示的步骤数量\n  const [failedStep, setFailedStep] = useState(null); // 记录失败的步骤\n  const [showToast, setShowToast] = useState(false); // 控制toast显示\n  const [productProcessingStep, setProductProcessingStep] = useState(0); // 商品加权进度步骤\n  const [startTime, setStartTime] = useState(null); // 执行开始时间\n  const [elapsedTime, setElapsedTime] = useState(0); // 累计执行时间（秒）\n  const intervalRef = useRef(null); // 定时器引用\n\n  // 工作流步骤定义\n  const workflowSteps = [{\n    id: 'login',\n    name: '登录',\n    runningDescription: '正在验证登录信息...',\n    completedDescription: '已完成登录【Temu账号】名下的店铺1',\n    duration: 2000,\n    status: 'pending'\n  }, {\n    id: 'product_processing',\n    name: '商品流量加速',\n    runningDescription: '正在为商品流量加速...',\n    completedDescription: '全部商品流量加速任务已完成！所有商品已按规则完成流量加速。',\n    duration: 4000,\n    status: 'pending',\n    progressSteps: ['商品流量加速任务已启动：正在按照预设规则对所有商品进行处理，单个商品处理时间约20-30秒', '商品SPU ID: 204845849 已开启30天普通流量加速加权。开始处理下一个商品...']\n  }, {\n    id: 'result',\n    name: '结果',\n    runningDescription: '正在生成执行结果...',\n    completedDescription: '本次任务执行完毕。共成功处理48个商品。另有2个商品未能成功，原因如下：【商品名称A】因AI判断其价格为6.2，高于5的设定而被跳过；【商品名称B】因页面长时间无法打开而执行失败。',\n    duration: 1000,\n    status: 'pending'\n  }];\n  const [steps, setSteps] = useState(workflowSteps);\n\n  // 时间统计效果\n  useEffect(() => {\n    if (workflowStatus === 'running' && startTime) {\n      intervalRef.current = setInterval(() => {\n        const now = Date.now();\n        const elapsed = Math.floor((now - startTime) / 1000);\n        setElapsedTime(elapsed);\n      }, 1000);\n    } else {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n        intervalRef.current = null;\n      }\n    }\n    return () => {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n    };\n  }, [workflowStatus, startTime]);\n\n  // 格式化时间显示\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // 获取步骤描述\n  const getStepDescription = step => {\n    if (step.status === 'running') {\n      // 商品加权步骤显示动态进度文案\n      if (step.id === 'product_processing' && step.progressSteps) {\n        return step.progressSteps[productProcessingStep] || step.runningDescription;\n      }\n      return step.runningDescription;\n    } else if (step.status === 'completed') {\n      return step.completedDescription;\n    } else if (step.status === 'failed') {\n      return `执行失败：${step.runningDescription}`;\n    }\n    return '等待执行...';\n  };\n\n  // 模拟工作流执行\n  const executeWorkflow = async () => {\n    setIsRunning(true);\n    setWorkflowStatus('running');\n    setFailedStep(null);\n\n    // 启动计时器\n    const now = Date.now();\n    setStartTime(now);\n    setElapsedTime(0);\n\n    // 从当前失败步骤或第一步开始\n    const startStep = failedStep !== null ? failedStep : 0;\n    setCurrentStep(startStep);\n\n    // 如果是重新开始，重置步骤状态\n    if (failedStep === null) {\n      setVisibleSteps(0);\n      setSteps(prev => prev.map(step => ({\n        ...step,\n        status: 'pending'\n      })));\n    }\n    for (let i = startStep; i < steps.length; i++) {\n      setCurrentStep(i);\n\n      // 显示当前步骤（如果还未显示）\n      if (i + 1 > visibleSteps) {\n        setVisibleSteps(i + 1);\n        // 确保新显示的步骤处于pending状态\n        setSteps(prev => prev.map((step, index) => index === i ? {\n          ...step,\n          status: 'pending'\n        } : step));\n        await new Promise(resolve => setTimeout(resolve, 300)); // 短暂延迟显示步骤\n      }\n\n      // 设置当前步骤为运行中\n      setSteps(prev => prev.map((step, index) => index === i ? {\n        ...step,\n        status: 'running'\n      } : step));\n\n      // 商品加权步骤的动态文案切换\n      if (steps[i].id === 'product_processing' && steps[i].progressSteps) {\n        const progressSteps = steps[i].progressSteps;\n        for (let j = 0; j < progressSteps.length; j++) {\n          setProductProcessingStep(j);\n          await new Promise(resolve => setTimeout(resolve, steps[i].duration / progressSteps.length));\n        }\n      } else {\n        // 等待步骤完成\n        await new Promise(resolve => setTimeout(resolve, steps[i].duration));\n      }\n\n      // 随机失败演示（15%概率，在第2或第3步）\n      const shouldFail = Math.random() < 0.15 && (i === 1 || i === 2);\n      if (shouldFail) {\n        setSteps(prev => prev.map((step, index) => index === i ? {\n          ...step,\n          status: 'failed'\n        } : step));\n        setWorkflowStatus('failed');\n        setFailedStep(i);\n        setIsRunning(false);\n        return;\n      }\n\n      // 设置步骤为完成\n      setSteps(prev => prev.map((step, index) => index === i ? {\n        ...step,\n        status: 'completed'\n      } : step));\n\n      // 完成一步后显示下一步（如果不是最后一步）\n      if (i < steps.length - 1) {\n        setVisibleSteps(i + 2);\n        // 确保下一步显示为pending状态\n        setSteps(prev => prev.map((step, index) => index === i + 1 ? {\n          ...step,\n          status: 'pending'\n        } : step));\n        await new Promise(resolve => setTimeout(resolve, 500)); // 延迟显示下一步\n      }\n    }\n    setWorkflowStatus('completed');\n    setFailedStep(null);\n    setIsRunning(false);\n  };\n\n  // 重试失败的步骤\n  const retryFromFailed = () => {\n    if (failedStep !== null) {\n      // 重置失败步骤的状态\n      setSteps(prev => prev.map((step, index) => index === failedStep ? {\n        ...step,\n        status: 'pending'\n      } : step));\n      executeWorkflow();\n    }\n  };\n\n  // 重置整个工作流\n  const resetWorkflow = () => {\n    setIsRunning(false);\n    setWorkflowStatus('idle');\n    setCurrentStep(0);\n    setVisibleSteps(0);\n    setFailedStep(null);\n    setProductProcessingStep(0);\n    setSteps(workflowSteps.map(step => ({\n      ...step,\n      status: 'pending'\n    })));\n  };\n\n  // 保存计划\n  const savePlan = () => {\n    setShowToast(true);\n    setTimeout(() => setShowToast(false), 3000);\n  };\n\n  // 模拟失败状态\n  const simulateFailure = () => {\n    setVisibleSteps(3);\n    setSteps(prev => prev.map((step, index) => {\n      if (index === 0) return {\n        ...step,\n        status: 'completed'\n      };\n      if (index === 1) return {\n        ...step,\n        status: 'completed'\n      };\n      if (index === 2) return {\n        ...step,\n        status: 'failed'\n      };\n      return step;\n    }));\n    setWorkflowStatus('failed');\n    setFailedStep(2);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg overflow-hidden border border-white/20\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-blue-500 text-white p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-bold\",\n              children: \"AI\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold\",\n              children: \"Temu\\u81EA\\u52A8\\u5316\\u52A9\\u624B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-100 text-sm\",\n              children: \"\\u6D41\\u91CF\\u52A0\\u901F\\u81EA\\u52A8\\u5316\\u6267\\u884C\\u4E2D...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-100 rounded-lg p-4 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-800 mb-2\",\n              children: \"\\u597D\\u7684\\uFF0C\\u5F00\\u59CB\\u6267\\u884C\\u8BA1\\u5212\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600 bg-white rounded-lg p-3 border\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: \"\\u6267\\u884C\\u914D\\u7F6E\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this), \"\\u666E\\u901A\\u6D41\\u91CF\\u52A0\\u6743\\u6863\\u4F4D\\uFF0C\\u4EF7\\u683C\\u8303\\u56F44-6\\u7F8E\\u5143\\uFF0C\\u65F6\\u654830\\u5929\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: steps.slice(0, visibleSteps).map((step, index) => {\n            const nextStep = steps[index + 1];\n            const shouldShowConnector = index < visibleSteps - 1 && index < steps.length - 1;\n            const connectorColor = step.status === 'completed' ? 'bg-green-500' : 'bg-gray-300';\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [shouldShowConnector && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `absolute left-4 top-8 w-0.5 transition-all duration-500 z-0 ${connectorColor}`,\n                style: {\n                  height: '3rem' // 固定高度，只连接到下一个步骤的起始位置\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-4 relative z-10 pb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0 relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-white rounded-full absolute inset-0 z-10 border border-gray-100\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 relative z-20 shadow-sm ${step.status === 'completed' ? 'bg-green-500 text-white' : step.status === 'failed' ? 'bg-red-500 text-white' : step.status === 'running' ? 'bg-blue-500 text-white' : step.status === 'pending' ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600'}`,\n                    children: [step.status === 'completed' && /*#__PURE__*/_jsxDEV(CheckCircle, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 284,\n                      columnNumber: 57\n                    }, this), step.status === 'failed' && /*#__PURE__*/_jsxDEV(XCircle, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 285,\n                      columnNumber: 54\n                    }, this), step.status === 'running' && /*#__PURE__*/_jsxDEV(Loader2, {\n                      className: \"w-5 h-5 animate-spin\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 286,\n                      columnNumber: 55\n                    }, this), step.status === 'pending' && /*#__PURE__*/_jsxDEV(Loader2, {\n                      className: \"w-5 h-5 animate-spin\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 287,\n                      columnNumber: 55\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 277,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0 pt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-lg font-medium text-gray-900\",\n                      children: step.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 294,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 21\n                  }, this), step.status !== 'failed' && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-600 leading-relaxed\",\n                    children: step.id === 'result' && step.status === 'completed' ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"font-medium mb-2\",\n                        children: \"\\u672C\\u6B21\\u4EFB\\u52A1\\u6267\\u884C\\u5B8C\\u6BD5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 302,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-2\",\n                        children: [\"\\u5171\\u6210\\u529F\\u5904\\u7406 \", /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-semibold text-green-600\",\n                          children: \"48\\u4E2A\\u5546\\u54C1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 303,\n                          columnNumber: 57\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 303,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-2\",\n                        children: [\"\\u5176\\u4E2D \", /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-semibold text-orange-600\",\n                          children: \"2\\u4E2A\\u5546\\u54C1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 304,\n                          columnNumber: 54\n                        }, this), \" \\u4E0D\\u7B26\\u5408\\u6761\\u4EF6\\uFF0C\\u5546\\u54C1\\u5982\\u4E0B\\uFF1A\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 304,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-1 pl-4 border-l-2 border-gray-200\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"font-medium\",\n                            children: \"SPU ID\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 306,\n                            columnNumber: 38\n                          }, this), \"\\uFF1A204845842\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 306,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"font-medium\",\n                            children: \"SPU ID\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 307,\n                            columnNumber: 38\n                          }, this), \"\\uFF1A204845843\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 307,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 305,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-2\",\n                        children: [\"\\u53E6\\u6709 \", /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-semibold text-orange-600\",\n                          children: \"2\\u4E2A\\u5546\\u54C1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 309,\n                          columnNumber: 54\n                        }, this), \" \\u5904\\u7406\\u5931\\u8D25\\uFF0C\\u5546\\u54C1\\u5982\\u4E0B\\uFF1A\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 309,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"space-y-1 pl-4 border-l-2 border-gray-200\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"font-medium\",\n                            children: \"SPU ID\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 311,\n                            columnNumber: 38\n                          }, this), \"\\uFF1A204845849\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 311,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"font-medium\",\n                            children: \"SPU ID\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 312,\n                            columnNumber: 38\n                          }, this), \"\\uFF1A204845844\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 312,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 310,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 301,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: getStepDescription(step)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 316,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 23\n                  }, this), step.status === 'failed' && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-red-600 text-sm\",\n                    children: getStepDescription(step)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 25\n                  }, this), step.status === 'failed' && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: retryFromFailed,\n                      disabled: isRunning,\n                      className: \"p-1.5 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-full transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 active:translate-y-0\",\n                      title: \"\\u91CD\\u8BD5\",\n                      children: /*#__PURE__*/_jsxDEV(RefreshCw, {\n                        className: \"w-3 h-3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 337,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 331,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 23\n                  }, this), step.status === 'completed' && workflowStatus === 'completed' && index === steps.length - 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-3 -ml-12 flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"inline-block bg-green-50 border border-green-200 rounded-full px-4 py-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-green-700 text-sm whitespace-nowrap\",\n                        children: \"\\u5DF2\\u5B8C\\u6210\\u5F53\\u524D\\u6240\\u6709\\u8BA1\\u5212\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 347,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 346,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: savePlan,\n                      className: \"p-1 text-gray-600 hover:text-blue-600 transition-all duration-200 hover:scale-110\",\n                      title: \"\\u4FDD\\u5B58\\u8BA1\\u5212\",\n                      children: /*#__PURE__*/_jsxDEV(Save, {\n                        className: \"w-4 h-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 358,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 353,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 19\n              }, this)]\n            }, step.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-8 pt-4 border-t border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [workflowStatus === 'idle' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: executeWorkflow,\n                  disabled: isRunning,\n                  className: \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Play, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 380,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u5F00\\u59CB\\u6267\\u884C\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: simulateFailure,\n                  className: \"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u6A21\\u62DF\\u5931\\u8D25\\u72B6\\u6001\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 388,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true), workflowStatus === 'running' && /*#__PURE__*/_jsxDEV(\"button\", {\n                disabled: true,\n                className: \"bg-gradient-to-r from-gray-400 to-gray-500 text-white px-6 py-3 rounded-xl text-sm font-medium cursor-not-allowed flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(Loader2, {\n                  className: \"w-4 h-4 animate-spin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u6267\\u884C\\u4E2D...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 19\n              }, this), workflowStatus === 'completed' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: resetWorkflow,\n                  className: \"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u6267\\u884C\\u5B8C\\u6210\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: simulateFailure,\n                  className: \"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 416,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u6A21\\u62DF\\u5931\\u8D25\\u72B6\\u6001\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true), workflowStatus === 'failed' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: retryFromFailed,\n                  disabled: isRunning,\n                  className: \"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 429,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u91CD\\u8BD5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: resetWorkflow,\n                  className: \"bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0\",\n                  children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: resetWorkflow,\n              className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:shadow-sm\",\n              title: \"\\u91CD\\u65B0\\u5F00\\u59CB\",\n              children: /*#__PURE__*/_jsxDEV(RefreshCw, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this), workflowStatus === 'failed' && failedStep !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200/50 rounded-xl p-6 shadow-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg\",\n                children: /*#__PURE__*/_jsxDEV(AlertCircle, {\n                  className: \"w-5 h-5 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-red-700 font-semibold text-base mb-1\",\n                  children: \"\\u6267\\u884C\\u4E2D\\u65AD\\uFF0C\\u9700\\u8981\\u5904\\u7406\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-red-600 text-sm\",\n                  children: [\"\\u7B2C \", failedStep + 1, \" \\u6B65\\u51FA\\u73B0\\u95EE\\u9898\\uFF0C\\u53EF\\u70B9\\u51FB\\u91CD\\u8BD5\\u7EE7\\u7EED\\u6267\\u884C\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 7\n    }, this), showToast && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-4 right-4 z-50 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg flex items-center space-x-2 animate-pulse\",\n      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n        className: \"w-5 h-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"\\u4FDD\\u5B58\\u8BA1\\u5212\\u6210\\u529F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 481,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 479,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 222,\n    columnNumber: 5\n  }, this);\n};\n_s(TemuWorkflowDemo, \"l8maJ+cvOvxkZqnqhu82Df0YvXo=\");\n_c = TemuWorkflowDemo;\nexport default TemuWorkflowDemo;\nvar _c;\n$RefreshReg$(_c, \"TemuWorkflowDemo\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Play", "RefreshCw", "CheckCircle", "XCircle", "AlertCircle", "Loader2", "Save", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TemuWorkflowDemo", "_s", "currentStep", "setCurrentStep", "isRunning", "setIsRunning", "workflowStatus", "setWorkflowStatus", "visibleSteps", "setVisibleSteps", "failedStep", "setFailedStep", "showToast", "setShowToast", "productProcessingStep", "setProductProcessingStep", "startTime", "setStartTime", "elapsedTime", "setElapsedTime", "intervalRef", "workflowSteps", "id", "name", "runningDescription", "completedDescription", "duration", "status", "progressSteps", "steps", "setSteps", "current", "setInterval", "now", "Date", "elapsed", "Math", "floor", "clearInterval", "formatTime", "seconds", "mins", "secs", "toString", "padStart", "getStepDescription", "step", "executeWorkflow", "startStep", "prev", "map", "i", "length", "index", "Promise", "resolve", "setTimeout", "j", "shouldFail", "random", "retryFromFailed", "resetWorkflow", "savePlan", "simulateFailure", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "slice", "nextStep", "shouldShowConnector", "connectorColor", "style", "height", "onClick", "disabled", "title", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowDemo.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { Play, RefreshCw, CheckCircle, XCircle, AlertCircle, Loader2, Save } from 'lucide-react';\n\nconst TemuWorkflowDemo = () => {\n  const [currentStep, setCurrentStep] = useState(0);\n  const [isRunning, setIsRunning] = useState(false);\n  const [workflowStatus, setWorkflowStatus] = useState('idle'); // idle, running, completed, failed\n  const [visibleSteps, setVisibleSteps] = useState(0); // 控制显示的步骤数量\n  const [failedStep, setFailedStep] = useState(null); // 记录失败的步骤\n  const [showToast, setShowToast] = useState(false); // 控制toast显示\n  const [productProcessingStep, setProductProcessingStep] = useState(0); // 商品加权进度步骤\n  const [startTime, setStartTime] = useState(null); // 执行开始时间\n  const [elapsedTime, setElapsedTime] = useState(0); // 累计执行时间（秒）\n  const intervalRef = useRef(null); // 定时器引用\n\n  // 工作流步骤定义\n  const workflowSteps = [\n    {\n      id: 'login',\n      name: '登录',\n      runningDescription: '正在验证登录信息...',\n      completedDescription: '已完成登录【Temu账号】名下的店铺1',\n      duration: 2000,\n      status: 'pending'\n    },\n    {\n      id: 'product_processing',\n      name: '商品流量加速',\n      runningDescription: '正在为商品流量加速...',\n      completedDescription: '全部商品流量加速任务已完成！所有商品已按规则完成流量加速。',\n      duration: 4000,\n      status: 'pending',\n      progressSteps: [\n        '商品流量加速任务已启动：正在按照预设规则对所有商品进行处理，单个商品处理时间约20-30秒',\n        '商品SPU ID: 204845849 已开启30天普通流量加速加权。开始处理下一个商品...'\n      ]\n    },\n    {\n      id: 'result',\n      name: '结果',\n      runningDescription: '正在生成执行结果...',\n      completedDescription: '本次任务执行完毕。共成功处理48个商品。另有2个商品未能成功，原因如下：【商品名称A】因AI判断其价格为6.2，高于5的设定而被跳过；【商品名称B】因页面长时间无法打开而执行失败。',\n      duration: 1000,\n      status: 'pending'\n    }\n  ];\n\n  const [steps, setSteps] = useState(workflowSteps);\n\n  // 时间统计效果\n  useEffect(() => {\n    if (workflowStatus === 'running' && startTime) {\n      intervalRef.current = setInterval(() => {\n        const now = Date.now();\n        const elapsed = Math.floor((now - startTime) / 1000);\n        setElapsedTime(elapsed);\n      }, 1000);\n    } else {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n        intervalRef.current = null;\n      }\n    }\n\n    return () => {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n    };\n  }, [workflowStatus, startTime]);\n\n  // 格式化时间显示\n  const formatTime = (seconds) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // 获取步骤描述\n  const getStepDescription = (step) => {\n    if (step.status === 'running') {\n      // 商品加权步骤显示动态进度文案\n      if (step.id === 'product_processing' && step.progressSteps) {\n        return step.progressSteps[productProcessingStep] || step.runningDescription;\n      }\n      return step.runningDescription;\n    } else if (step.status === 'completed') {\n      return step.completedDescription;\n    } else if (step.status === 'failed') {\n      return `执行失败：${step.runningDescription}`;\n    }\n    return '等待执行...';\n  };\n\n  // 模拟工作流执行\n  const executeWorkflow = async () => {\n    setIsRunning(true);\n    setWorkflowStatus('running');\n    setFailedStep(null);\n\n    // 启动计时器\n    const now = Date.now();\n    setStartTime(now);\n    setElapsedTime(0);\n\n    // 从当前失败步骤或第一步开始\n    const startStep = failedStep !== null ? failedStep : 0;\n    setCurrentStep(startStep);\n\n    // 如果是重新开始，重置步骤状态\n    if (failedStep === null) {\n      setVisibleSteps(0);\n      setSteps(prev => prev.map(step => ({ ...step, status: 'pending' })));\n    }\n\n    for (let i = startStep; i < steps.length; i++) {\n      setCurrentStep(i);\n\n      // 显示当前步骤（如果还未显示）\n      if (i + 1 > visibleSteps) {\n        setVisibleSteps(i + 1);\n        // 确保新显示的步骤处于pending状态\n        setSteps(prev => prev.map((step, index) =>\n          index === i ? { ...step, status: 'pending' } : step\n        ));\n        await new Promise(resolve => setTimeout(resolve, 300)); // 短暂延迟显示步骤\n      }\n\n      // 设置当前步骤为运行中\n      setSteps(prev => prev.map((step, index) =>\n        index === i ? { ...step, status: 'running' } : step\n      ));\n\n      // 商品加权步骤的动态文案切换\n      if (steps[i].id === 'product_processing' && steps[i].progressSteps) {\n        const progressSteps = steps[i].progressSteps;\n        for (let j = 0; j < progressSteps.length; j++) {\n          setProductProcessingStep(j);\n          await new Promise(resolve => setTimeout(resolve, steps[i].duration / progressSteps.length));\n        }\n      } else {\n        // 等待步骤完成\n        await new Promise(resolve => setTimeout(resolve, steps[i].duration));\n      }\n\n      // 随机失败演示（15%概率，在第2或第3步）\n      const shouldFail = Math.random() < 0.15 && (i === 1 || i === 2);\n\n      if (shouldFail) {\n        setSteps(prev => prev.map((step, index) =>\n          index === i ? { ...step, status: 'failed' } : step\n        ));\n        setWorkflowStatus('failed');\n        setFailedStep(i);\n        setIsRunning(false);\n        return;\n      }\n\n      // 设置步骤为完成\n      setSteps(prev => prev.map((step, index) =>\n        index === i ? { ...step, status: 'completed' } : step\n      ));\n\n      // 完成一步后显示下一步（如果不是最后一步）\n      if (i < steps.length - 1) {\n        setVisibleSteps(i + 2);\n        // 确保下一步显示为pending状态\n        setSteps(prev => prev.map((step, index) =>\n          index === i + 1 ? { ...step, status: 'pending' } : step\n        ));\n        await new Promise(resolve => setTimeout(resolve, 500)); // 延迟显示下一步\n      }\n    }\n\n    setWorkflowStatus('completed');\n    setFailedStep(null);\n    setIsRunning(false);\n  };\n\n  // 重试失败的步骤\n  const retryFromFailed = () => {\n    if (failedStep !== null) {\n      // 重置失败步骤的状态\n      setSteps(prev => prev.map((step, index) => \n        index === failedStep ? { ...step, status: 'pending' } : step\n      ));\n      executeWorkflow();\n    }\n  };\n\n  // 重置整个工作流\n  const resetWorkflow = () => {\n    setIsRunning(false);\n    setWorkflowStatus('idle');\n    setCurrentStep(0);\n    setVisibleSteps(0);\n    setFailedStep(null);\n    setProductProcessingStep(0);\n    setSteps(workflowSteps.map(step => ({ ...step, status: 'pending' })));\n  };\n\n  // 保存计划\n  const savePlan = () => {\n    setShowToast(true);\n    setTimeout(() => setShowToast(false), 3000);\n  };\n\n  // 模拟失败状态\n  const simulateFailure = () => {\n    setVisibleSteps(3);\n    setSteps(prev => prev.map((step, index) => {\n      if (index === 0) return { ...step, status: 'completed' };\n      if (index === 1) return { ...step, status: 'completed' };\n      if (index === 2) return { ...step, status: 'failed' };\n      return step;\n    }));\n    setWorkflowStatus('failed');\n    setFailedStep(2);\n  };\n\n  return (\n    <div className=\"w-full\">\n      {/* AI对话框容器 */}\n      <div className=\"bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg overflow-hidden border border-white/20\">\n        {/* 对话框头部 */}\n        <div className=\"bg-blue-500 text-white p-4\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\">\n              <span className=\"text-sm font-bold\">AI</span>\n            </div>\n            <div>\n              <h3 className=\"font-semibold\">Temu自动化助手</h3>\n              <p className=\"text-blue-100 text-sm\">流量加速自动化执行中...</p>\n            </div>\n          </div>\n        </div>\n\n        {/* 对话内容 */}\n        <div className=\"p-6\">\n          {/* AI消息 */}\n          <div className=\"mb-6\">\n            <div className=\"bg-gray-100 rounded-lg p-4 mb-4\">\n              <p className=\"text-gray-800 mb-2\">好的，开始执行计划</p>\n              <div className=\"text-sm text-gray-600 bg-white rounded-lg p-3 border\">\n                <span className=\"font-medium\">执行配置：</span>普通流量加权档位，价格范围4-6美元，时效30天\n              </div>\n            </div>\n          </div>\n\n          {/* 优化后的时间线步骤列表 - 完美连接线 */}\n          <div className=\"relative\">\n            {steps.slice(0, visibleSteps).map((step, index) => {\n              const nextStep = steps[index + 1];\n              const shouldShowConnector = index < visibleSteps - 1 && index < steps.length - 1;\n              const connectorColor = step.status === 'completed' ? 'bg-green-500' : 'bg-gray-300';\n\n              return (\n                <div key={step.id} className=\"relative\">\n                  {/* 连接线 - 只在步骤完成后显示绿色，否则显示灰色 */}\n                  {shouldShowConnector && (\n                    <div\n                      className={`absolute left-4 top-8 w-0.5 transition-all duration-500 z-0 ${connectorColor}`}\n                      style={{\n                        height: '3rem' // 固定高度，只连接到下一个步骤的起始位置\n                      }}\n                    ></div>\n                  )}\n\n                  {/* 步骤内容容器 */}\n                  <div className=\"flex items-start space-x-4 relative z-10 pb-6\">\n                    {/* 状态图标容器 - 优化层级确保完美覆盖 */}\n                    <div className=\"flex-shrink-0 relative\">\n                      {/* 图标背景圆圈 - 确保完全覆盖连接线 */}\n                      <div className=\"w-8 h-8 bg-white rounded-full absolute inset-0 z-10 border border-gray-100\"></div>\n\n                      {/* 状态图标 - 最高层级 */}\n                      <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 relative z-20 shadow-sm ${\n                        step.status === 'completed' ? 'bg-green-500 text-white' :\n                        step.status === 'failed' ? 'bg-red-500 text-white' :\n                        step.status === 'running' ? 'bg-blue-500 text-white' :\n                        step.status === 'pending' ? 'bg-blue-500 text-white' :\n                        'bg-gray-300 text-gray-600'\n                      }`}>\n                        {step.status === 'completed' && <CheckCircle className=\"w-5 h-5\" />}\n                        {step.status === 'failed' && <XCircle className=\"w-5 h-5\" />}\n                        {step.status === 'running' && <Loader2 className=\"w-5 h-5 animate-spin\" />}\n                        {step.status === 'pending' && <Loader2 className=\"w-5 h-5 animate-spin\" />}\n                      </div>\n                    </div>\n\n                  {/* 步骤内容 */}\n                  <div className=\"flex-1 min-w-0 pt-1\">\n                    <div className=\"mb-2\">\n                      <h4 className=\"text-lg font-medium text-gray-900\">{step.name}</h4>\n                    </div>\n\n                    {/* 只在非失败状态时显示描述 */}\n                    {step.status !== 'failed' && (\n                      <div className=\"text-sm text-gray-600 leading-relaxed\">\n                        {step.id === 'result' && step.status === 'completed' ? (\n                          <div>\n                            <div className=\"font-medium mb-2\">本次任务执行完毕</div>\n                            <div className=\"mb-2\">共成功处理 <span className=\"font-semibold text-green-600\">48个商品</span></div>\n                            <div className=\"mb-2\">其中 <span className=\"font-semibold text-orange-600\">2个商品</span> 不符合条件，商品如下：</div>\n                            <div className=\"space-y-1 pl-4 border-l-2 border-gray-200\">\n                              <div>• <span className=\"font-medium\">SPU ID</span>：204845842</div>\n                              <div>• <span className=\"font-medium\">SPU ID</span>：204845843</div>\n                            </div>\n                            <div className=\"mb-2\">另有 <span className=\"font-semibold text-orange-600\">2个商品</span> 处理失败，商品如下：</div>\n                            <div className=\"space-y-1 pl-4 border-l-2 border-gray-200\">\n                              <div>• <span className=\"font-medium\">SPU ID</span>：204845849</div>\n                              <div>• <span className=\"font-medium\">SPU ID</span>：204845844</div>\n                            </div>\n                          </div>\n                        ) : (\n                          <p>{getStepDescription(step)}</p>\n                        )}\n                      </div>\n                    )}\n\n                    {/* 失败时的状态消息容器 */}\n                    {step.status === 'failed' && (\n                        <p className=\"text-red-600 text-sm\">\n                          {getStepDescription(step)}\n                        </p>\n                    )}\n\n                    {/* 重试按钮 - 在失败状态消息容器下方，与步骤标题左对齐 */}\n                    {step.status === 'failed' && (\n                      <div className=\"mt-3\">\n                        <button\n                          onClick={retryFromFailed}\n                          disabled={isRunning}\n                          className=\"p-1.5 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-full transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 active:translate-y-0\"\n                          title=\"重试\"\n                        >\n                          <RefreshCw className=\"w-3 h-3\" />\n                        </button>\n                      </div>\n                    )}\n\n                    {/* 成功状态：椭圆形消息容器和保存按钮布局 */}\n                    {step.status === 'completed' && workflowStatus === 'completed' && index === steps.length - 1 && (\n                      <div className=\"mt-3 -ml-12 flex items-center space-x-2\">\n                        {/* 成功状态消息容器 - 椭圆形态，宽度跟随文字，左对齐到绿色图标 */}\n                        <div className=\"inline-block bg-green-50 border border-green-200 rounded-full px-4 py-2\">\n                          <p className=\"text-green-700 text-sm whitespace-nowrap\">\n                           已完成当前所有计划\n                          </p>\n                        </div>\n\n                        {/* 保存按钮 - 纯图标，在椭圆形容器右侧 */}\n                        <button\n                          onClick={savePlan}\n                          className=\"p-1 text-gray-600 hover:text-blue-600 transition-all duration-200 hover:scale-110\"\n                          title=\"保存计划\"\n                        >\n                          <Save className=\"w-4 h-4\" />\n                        </button>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            );\n            })}\n          </div>\n\n          {/* 操作按钮区域 */}\n          <div className=\"mt-8 pt-4 border-t border-gray-200\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                {workflowStatus === 'idle' && (\n                  <>\n                    <button\n                      onClick={executeWorkflow}\n                      disabled={isRunning}\n                      className=\"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                    >\n                      <Play className=\"w-4 h-4\" />\n                      <span>开始执行</span>\n                    </button>\n                    <button\n                      onClick={simulateFailure}\n                      className=\"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                    >\n                      <AlertCircle className=\"w-4 h-4\" />\n                      <span>模拟失败状态</span>\n                    </button>\n                  </>\n                )}\n\n                {workflowStatus === 'running' && (\n                  <button\n                    disabled\n                    className=\"bg-gradient-to-r from-gray-400 to-gray-500 text-white px-6 py-3 rounded-xl text-sm font-medium cursor-not-allowed flex items-center space-x-2\"\n                  >\n                    <Loader2 className=\"w-4 h-4 animate-spin\" />\n                    <span>执行中...</span>\n                  </button>\n                )}\n\n                {workflowStatus === 'completed' && (\n                  <>\n                    <button\n                      onClick={resetWorkflow}\n                      className=\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                    >\n                      <CheckCircle className=\"w-4 h-4\" />\n                      <span>执行完成</span>\n                    </button>\n                    <button\n                      onClick={simulateFailure}\n                      className=\"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                    >\n                      <AlertCircle className=\"w-4 h-4\" />\n                      <span>模拟失败状态</span>\n                    </button>\n                  </>\n                )}\n\n                {workflowStatus === 'failed' && (\n                  <div className=\"flex items-center space-x-3\">\n                    <button\n                      onClick={retryFromFailed}\n                      disabled={isRunning}\n                      className=\"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                    >\n                      <RefreshCw className=\"w-4 h-4\" />\n                      <span>重试</span>\n                    </button>\n                    <button\n                      onClick={resetWorkflow}\n                      className=\"bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0\"\n                    >\n                      重新开始\n                    </button>\n                  </div>\n                )}\n\n\n              </div>\n\n              {/* 刷新按钮 */}\n              <button\n                onClick={resetWorkflow}\n                className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:shadow-sm\"\n                title=\"重新开始\"\n              >\n                <RefreshCw className=\"w-4 h-4\" />\n              </button>\n            </div>\n\n   \n\n            {workflowStatus === 'failed' && failedStep !== null && (\n              <div className=\"mt-6 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200/50 rounded-xl p-6 shadow-sm\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"w-10 h-10 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg\">\n                    <AlertCircle className=\"w-5 h-5 text-white\" />\n                  </div>\n                  <div>\n                    <div className=\"text-red-700 font-semibold text-base mb-1\">\n                      执行中断，需要处理\n                    </div>\n                    <div className=\"text-red-600 text-sm\">\n                      第 {failedStep + 1} 步出现问题，可点击重试继续执行\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Toast 通知 */}\n      {showToast && (\n        <div className=\"fixed top-4 right-4 z-50 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg flex items-center space-x-2 animate-pulse\">\n          <CheckCircle className=\"w-5 h-5\" />\n          <span>保存计划成功</span>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TemuWorkflowDemo;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,IAAI,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,WAAW,EAAEC,OAAO,EAAEC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjG,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoB,cAAc,EAAEC,iBAAiB,CAAC,GAAGrB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACrD,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC4B,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACvE,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAClD,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACnD,MAAMkC,WAAW,GAAGhC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;EAElC;EACA,MAAMiC,aAAa,GAAG,CACpB;IACEC,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,IAAI;IACVC,kBAAkB,EAAE,aAAa;IACjCC,oBAAoB,EAAE,qBAAqB;IAC3CC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE;EACV,CAAC,EACD;IACEL,EAAE,EAAE,oBAAoB;IACxBC,IAAI,EAAE,QAAQ;IACdC,kBAAkB,EAAE,cAAc;IAClCC,oBAAoB,EAAE,+BAA+B;IACrDC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,SAAS;IACjBC,aAAa,EAAE,CACb,+CAA+C,EAC/C,iDAAiD;EAErD,CAAC,EACD;IACEN,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,IAAI;IACVC,kBAAkB,EAAE,aAAa;IACjCC,oBAAoB,EAAE,4FAA4F;IAClHC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAM,CAACE,KAAK,EAAEC,QAAQ,CAAC,GAAG5C,QAAQ,CAACmC,aAAa,CAAC;;EAEjD;EACAlC,SAAS,CAAC,MAAM;IACd,IAAImB,cAAc,KAAK,SAAS,IAAIU,SAAS,EAAE;MAC7CI,WAAW,CAACW,OAAO,GAAGC,WAAW,CAAC,MAAM;QACtC,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;QACtB,MAAME,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACJ,GAAG,GAAGjB,SAAS,IAAI,IAAI,CAAC;QACpDG,cAAc,CAACgB,OAAO,CAAC;MACzB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM;MACL,IAAIf,WAAW,CAACW,OAAO,EAAE;QACvBO,aAAa,CAAClB,WAAW,CAACW,OAAO,CAAC;QAClCX,WAAW,CAACW,OAAO,GAAG,IAAI;MAC5B;IACF;IAEA,OAAO,MAAM;MACX,IAAIX,WAAW,CAACW,OAAO,EAAE;QACvBO,aAAa,CAAClB,WAAW,CAACW,OAAO,CAAC;MACpC;IACF,CAAC;EACH,CAAC,EAAE,CAACzB,cAAc,EAAEU,SAAS,CAAC,CAAC;;EAE/B;EACA,MAAMuB,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,IAAI,GAAGL,IAAI,CAACC,KAAK,CAACG,OAAO,GAAG,EAAE,CAAC;IACrC,MAAME,IAAI,GAAGF,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAClF,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAIC,IAAI,IAAK;IACnC,IAAIA,IAAI,CAACnB,MAAM,KAAK,SAAS,EAAE;MAC7B;MACA,IAAImB,IAAI,CAACxB,EAAE,KAAK,oBAAoB,IAAIwB,IAAI,CAAClB,aAAa,EAAE;QAC1D,OAAOkB,IAAI,CAAClB,aAAa,CAACd,qBAAqB,CAAC,IAAIgC,IAAI,CAACtB,kBAAkB;MAC7E;MACA,OAAOsB,IAAI,CAACtB,kBAAkB;IAChC,CAAC,MAAM,IAAIsB,IAAI,CAACnB,MAAM,KAAK,WAAW,EAAE;MACtC,OAAOmB,IAAI,CAACrB,oBAAoB;IAClC,CAAC,MAAM,IAAIqB,IAAI,CAACnB,MAAM,KAAK,QAAQ,EAAE;MACnC,OAAO,QAAQmB,IAAI,CAACtB,kBAAkB,EAAE;IAC1C;IACA,OAAO,SAAS;EAClB,CAAC;;EAED;EACA,MAAMuB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC1C,YAAY,CAAC,IAAI,CAAC;IAClBE,iBAAiB,CAAC,SAAS,CAAC;IAC5BI,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,MAAMsB,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACtBhB,YAAY,CAACgB,GAAG,CAAC;IACjBd,cAAc,CAAC,CAAC,CAAC;;IAEjB;IACA,MAAM6B,SAAS,GAAGtC,UAAU,KAAK,IAAI,GAAGA,UAAU,GAAG,CAAC;IACtDP,cAAc,CAAC6C,SAAS,CAAC;;IAEzB;IACA,IAAItC,UAAU,KAAK,IAAI,EAAE;MACvBD,eAAe,CAAC,CAAC,CAAC;MAClBqB,QAAQ,CAACmB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACJ,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEnB,MAAM,EAAE;MAAU,CAAC,CAAC,CAAC,CAAC;IACtE;IAEA,KAAK,IAAIwB,CAAC,GAAGH,SAAS,EAAEG,CAAC,GAAGtB,KAAK,CAACuB,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7ChD,cAAc,CAACgD,CAAC,CAAC;;MAEjB;MACA,IAAIA,CAAC,GAAG,CAAC,GAAG3C,YAAY,EAAE;QACxBC,eAAe,CAAC0C,CAAC,GAAG,CAAC,CAAC;QACtB;QACArB,QAAQ,CAACmB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KACpCA,KAAK,KAAKF,CAAC,GAAG;UAAE,GAAGL,IAAI;UAAEnB,MAAM,EAAE;QAAU,CAAC,GAAGmB,IACjD,CAAC,CAAC;QACF,MAAM,IAAIQ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;MAC1D;;MAEA;MACAzB,QAAQ,CAACmB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KACpCA,KAAK,KAAKF,CAAC,GAAG;QAAE,GAAGL,IAAI;QAAEnB,MAAM,EAAE;MAAU,CAAC,GAAGmB,IACjD,CAAC,CAAC;;MAEF;MACA,IAAIjB,KAAK,CAACsB,CAAC,CAAC,CAAC7B,EAAE,KAAK,oBAAoB,IAAIO,KAAK,CAACsB,CAAC,CAAC,CAACvB,aAAa,EAAE;QAClE,MAAMA,aAAa,GAAGC,KAAK,CAACsB,CAAC,CAAC,CAACvB,aAAa;QAC5C,KAAK,IAAI6B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7B,aAAa,CAACwB,MAAM,EAAEK,CAAC,EAAE,EAAE;UAC7C1C,wBAAwB,CAAC0C,CAAC,CAAC;UAC3B,MAAM,IAAIH,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE1B,KAAK,CAACsB,CAAC,CAAC,CAACzB,QAAQ,GAAGE,aAAa,CAACwB,MAAM,CAAC,CAAC;QAC7F;MACF,CAAC,MAAM;QACL;QACA,MAAM,IAAIE,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE1B,KAAK,CAACsB,CAAC,CAAC,CAACzB,QAAQ,CAAC,CAAC;MACtE;;MAEA;MACA,MAAMgC,UAAU,GAAGtB,IAAI,CAACuB,MAAM,CAAC,CAAC,GAAG,IAAI,KAAKR,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,CAAC;MAE/D,IAAIO,UAAU,EAAE;QACd5B,QAAQ,CAACmB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KACpCA,KAAK,KAAKF,CAAC,GAAG;UAAE,GAAGL,IAAI;UAAEnB,MAAM,EAAE;QAAS,CAAC,GAAGmB,IAChD,CAAC,CAAC;QACFvC,iBAAiB,CAAC,QAAQ,CAAC;QAC3BI,aAAa,CAACwC,CAAC,CAAC;QAChB9C,YAAY,CAAC,KAAK,CAAC;QACnB;MACF;;MAEA;MACAyB,QAAQ,CAACmB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KACpCA,KAAK,KAAKF,CAAC,GAAG;QAAE,GAAGL,IAAI;QAAEnB,MAAM,EAAE;MAAY,CAAC,GAAGmB,IACnD,CAAC,CAAC;;MAEF;MACA,IAAIK,CAAC,GAAGtB,KAAK,CAACuB,MAAM,GAAG,CAAC,EAAE;QACxB3C,eAAe,CAAC0C,CAAC,GAAG,CAAC,CAAC;QACtB;QACArB,QAAQ,CAACmB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KACpCA,KAAK,KAAKF,CAAC,GAAG,CAAC,GAAG;UAAE,GAAGL,IAAI;UAAEnB,MAAM,EAAE;QAAU,CAAC,GAAGmB,IACrD,CAAC,CAAC;QACF,MAAM,IAAIQ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;MAC1D;IACF;IAEAhD,iBAAiB,CAAC,WAAW,CAAC;IAC9BI,aAAa,CAAC,IAAI,CAAC;IACnBN,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAMuD,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIlD,UAAU,KAAK,IAAI,EAAE;MACvB;MACAoB,QAAQ,CAACmB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KACpCA,KAAK,KAAK3C,UAAU,GAAG;QAAE,GAAGoC,IAAI;QAAEnB,MAAM,EAAE;MAAU,CAAC,GAAGmB,IAC1D,CAAC,CAAC;MACFC,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMc,aAAa,GAAGA,CAAA,KAAM;IAC1BxD,YAAY,CAAC,KAAK,CAAC;IACnBE,iBAAiB,CAAC,MAAM,CAAC;IACzBJ,cAAc,CAAC,CAAC,CAAC;IACjBM,eAAe,CAAC,CAAC,CAAC;IAClBE,aAAa,CAAC,IAAI,CAAC;IACnBI,wBAAwB,CAAC,CAAC,CAAC;IAC3Be,QAAQ,CAACT,aAAa,CAAC6B,GAAG,CAACJ,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEnB,MAAM,EAAE;IAAU,CAAC,CAAC,CAAC,CAAC;EACvE,CAAC;;EAED;EACA,MAAMmC,QAAQ,GAAGA,CAAA,KAAM;IACrBjD,YAAY,CAAC,IAAI,CAAC;IAClB2C,UAAU,CAAC,MAAM3C,YAAY,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;EAC7C,CAAC;;EAED;EACA,MAAMkD,eAAe,GAAGA,CAAA,KAAM;IAC5BtD,eAAe,CAAC,CAAC,CAAC;IAClBqB,QAAQ,CAACmB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KAAK;MACzC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO;QAAE,GAAGP,IAAI;QAAEnB,MAAM,EAAE;MAAY,CAAC;MACxD,IAAI0B,KAAK,KAAK,CAAC,EAAE,OAAO;QAAE,GAAGP,IAAI;QAAEnB,MAAM,EAAE;MAAY,CAAC;MACxD,IAAI0B,KAAK,KAAK,CAAC,EAAE,OAAO;QAAE,GAAGP,IAAI;QAAEnB,MAAM,EAAE;MAAS,CAAC;MACrD,OAAOmB,IAAI;IACb,CAAC,CAAC,CAAC;IACHvC,iBAAiB,CAAC,QAAQ,CAAC;IAC3BI,aAAa,CAAC,CAAC,CAAC;EAClB,CAAC;EAED,oBACEd,OAAA;IAAKmE,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBAErBpE,OAAA;MAAKmE,SAAS,EAAC,2FAA2F;MAAAC,QAAA,gBAExGpE,OAAA;QAAKmE,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzCpE,OAAA;UAAKmE,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CpE,OAAA;YAAKmE,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChFpE,OAAA;cAAMmE,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACNxE,OAAA;YAAAoE,QAAA,gBACEpE,OAAA;cAAImE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5CxE,OAAA;cAAGmE,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxE,OAAA;QAAKmE,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAElBpE,OAAA;UAAKmE,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBpE,OAAA;YAAKmE,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9CpE,OAAA;cAAGmE,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC/CxE,OAAA;cAAKmE,SAAS,EAAC,sDAAsD;cAAAC,QAAA,gBACnEpE,OAAA;gBAAMmE,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,2HAC5C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxE,OAAA;UAAKmE,SAAS,EAAC,UAAU;UAAAC,QAAA,EACtBpC,KAAK,CAACyC,KAAK,CAAC,CAAC,EAAE9D,YAAY,CAAC,CAAC0C,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KAAK;YACjD,MAAMkB,QAAQ,GAAG1C,KAAK,CAACwB,KAAK,GAAG,CAAC,CAAC;YACjC,MAAMmB,mBAAmB,GAAGnB,KAAK,GAAG7C,YAAY,GAAG,CAAC,IAAI6C,KAAK,GAAGxB,KAAK,CAACuB,MAAM,GAAG,CAAC;YAChF,MAAMqB,cAAc,GAAG3B,IAAI,CAACnB,MAAM,KAAK,WAAW,GAAG,cAAc,GAAG,aAAa;YAEnF,oBACE9B,OAAA;cAAmBmE,SAAS,EAAC,UAAU;cAAAC,QAAA,GAEpCO,mBAAmB,iBAClB3E,OAAA;gBACEmE,SAAS,EAAE,+DAA+DS,cAAc,EAAG;gBAC3FC,KAAK,EAAE;kBACLC,MAAM,EAAE,MAAM,CAAC;gBACjB;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACP,eAGDxE,OAAA;gBAAKmE,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,gBAE5DpE,OAAA;kBAAKmE,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBAErCpE,OAAA;oBAAKmE,SAAS,EAAC;kBAA4E;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAGlGxE,OAAA;oBAAKmE,SAAS,EAAE,6GACdlB,IAAI,CAACnB,MAAM,KAAK,WAAW,GAAG,yBAAyB,GACvDmB,IAAI,CAACnB,MAAM,KAAK,QAAQ,GAAG,uBAAuB,GAClDmB,IAAI,CAACnB,MAAM,KAAK,SAAS,GAAG,wBAAwB,GACpDmB,IAAI,CAACnB,MAAM,KAAK,SAAS,GAAG,wBAAwB,GACpD,2BAA2B,EAC1B;oBAAAsC,QAAA,GACAnB,IAAI,CAACnB,MAAM,KAAK,WAAW,iBAAI9B,OAAA,CAACN,WAAW;sBAACyE,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAClEvB,IAAI,CAACnB,MAAM,KAAK,QAAQ,iBAAI9B,OAAA,CAACL,OAAO;sBAACwE,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAC3DvB,IAAI,CAACnB,MAAM,KAAK,SAAS,iBAAI9B,OAAA,CAACH,OAAO;sBAACsE,SAAS,EAAC;oBAAsB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACzEvB,IAAI,CAACnB,MAAM,KAAK,SAAS,iBAAI9B,OAAA,CAACH,OAAO;sBAACsE,SAAS,EAAC;oBAAsB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGRxE,OAAA;kBAAKmE,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAClCpE,OAAA;oBAAKmE,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnBpE,OAAA;sBAAImE,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEnB,IAAI,CAACvB;oBAAI;sBAAA2C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,EAGLvB,IAAI,CAACnB,MAAM,KAAK,QAAQ,iBACvB9B,OAAA;oBAAKmE,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EACnDnB,IAAI,CAACxB,EAAE,KAAK,QAAQ,IAAIwB,IAAI,CAACnB,MAAM,KAAK,WAAW,gBAClD9B,OAAA;sBAAAoE,QAAA,gBACEpE,OAAA;wBAAKmE,SAAS,EAAC,kBAAkB;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAChDxE,OAAA;wBAAKmE,SAAS,EAAC,MAAM;wBAAAC,QAAA,GAAC,iCAAM,eAAApE,OAAA;0BAAMmE,SAAS,EAAC,8BAA8B;0BAAAC,QAAA,EAAC;wBAAK;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC7FxE,OAAA;wBAAKmE,SAAS,EAAC,MAAM;wBAAAC,QAAA,GAAC,eAAG,eAAApE,OAAA;0BAAMmE,SAAS,EAAC,+BAA+B;0BAAAC,QAAA,EAAC;wBAAI;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,uEAAY;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACtGxE,OAAA;wBAAKmE,SAAS,EAAC,2CAA2C;wBAAAC,QAAA,gBACxDpE,OAAA;0BAAAoE,QAAA,GAAK,SAAE,eAAApE,OAAA;4BAAMmE,SAAS,EAAC,aAAa;4BAAAC,QAAA,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,mBAAU;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAClExE,OAAA;0BAAAoE,QAAA,GAAK,SAAE,eAAApE,OAAA;4BAAMmE,SAAS,EAAC,aAAa;4BAAAC,QAAA,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,mBAAU;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/D,CAAC,eACNxE,OAAA;wBAAKmE,SAAS,EAAC,MAAM;wBAAAC,QAAA,GAAC,eAAG,eAAApE,OAAA;0BAAMmE,SAAS,EAAC,+BAA+B;0BAAAC,QAAA,EAAC;wBAAI;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,iEAAW;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACrGxE,OAAA;wBAAKmE,SAAS,EAAC,2CAA2C;wBAAAC,QAAA,gBACxDpE,OAAA;0BAAAoE,QAAA,GAAK,SAAE,eAAApE,OAAA;4BAAMmE,SAAS,EAAC,aAAa;4BAAAC,QAAA,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,mBAAU;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAClExE,OAAA;0BAAAoE,QAAA,GAAK,SAAE,eAAApE,OAAA;4BAAMmE,SAAS,EAAC,aAAa;4BAAAC,QAAA,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,mBAAU;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/D,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,gBAENxE,OAAA;sBAAAoE,QAAA,EAAIpB,kBAAkB,CAACC,IAAI;oBAAC;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBACjC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CACN,EAGAvB,IAAI,CAACnB,MAAM,KAAK,QAAQ,iBACrB9B,OAAA;oBAAGmE,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAChCpB,kBAAkB,CAACC,IAAI;kBAAC;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CACN,EAGAvB,IAAI,CAACnB,MAAM,KAAK,QAAQ,iBACvB9B,OAAA;oBAAKmE,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnBpE,OAAA;sBACE+E,OAAO,EAAEhB,eAAgB;sBACzBiB,QAAQ,EAAEzE,SAAU;sBACpB4D,SAAS,EAAC,gQAAgQ;sBAC1Qc,KAAK,EAAC,cAAI;sBAAAb,QAAA,eAEVpE,OAAA,CAACP,SAAS;wBAAC0E,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CACN,EAGAvB,IAAI,CAACnB,MAAM,KAAK,WAAW,IAAIrB,cAAc,KAAK,WAAW,IAAI+C,KAAK,KAAKxB,KAAK,CAACuB,MAAM,GAAG,CAAC,iBAC1FvD,OAAA;oBAAKmE,SAAS,EAAC,yCAAyC;oBAAAC,QAAA,gBAEtDpE,OAAA;sBAAKmE,SAAS,EAAC,yEAAyE;sBAAAC,QAAA,eACtFpE,OAAA;wBAAGmE,SAAS,EAAC,0CAA0C;wBAAAC,QAAA,EAAC;sBAExD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC,eAGNxE,OAAA;sBACE+E,OAAO,EAAEd,QAAS;sBAClBE,SAAS,EAAC,mFAAmF;sBAC7Fc,KAAK,EAAC,0BAAM;sBAAAb,QAAA,eAEZpE,OAAA,CAACF,IAAI;wBAACqE,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAzGIvB,IAAI,CAACxB,EAAE;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0Gd,CAAC;UAER,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNxE,OAAA;UAAKmE,SAAS,EAAC,oCAAoC;UAAAC,QAAA,gBACjDpE,OAAA;YAAKmE,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDpE,OAAA;cAAKmE,SAAS,EAAC,6BAA6B;cAAAC,QAAA,GACzC3D,cAAc,KAAK,MAAM,iBACxBT,OAAA,CAAAE,SAAA;gBAAAkE,QAAA,gBACEpE,OAAA;kBACE+E,OAAO,EAAE7B,eAAgB;kBACzB8B,QAAQ,EAAEzE,SAAU;kBACpB4D,SAAS,EAAC,0SAA0S;kBAAAC,QAAA,gBAEpTpE,OAAA,CAACR,IAAI;oBAAC2E,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5BxE,OAAA;oBAAAoE,QAAA,EAAM;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACTxE,OAAA;kBACE+E,OAAO,EAAEb,eAAgB;kBACzBC,SAAS,EAAC,kQAAkQ;kBAAAC,QAAA,gBAE5QpE,OAAA,CAACJ,WAAW;oBAACuE,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnCxE,OAAA;oBAAAoE,QAAA,EAAM;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA,eACT,CACH,EAEA/D,cAAc,KAAK,SAAS,iBAC3BT,OAAA;gBACEgF,QAAQ;gBACRb,SAAS,EAAC,+IAA+I;gBAAAC,QAAA,gBAEzJpE,OAAA,CAACH,OAAO;kBAACsE,SAAS,EAAC;gBAAsB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5CxE,OAAA;kBAAAoE,QAAA,EAAM;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CACT,EAEA/D,cAAc,KAAK,WAAW,iBAC7BT,OAAA,CAAAE,SAAA;gBAAAkE,QAAA,gBACEpE,OAAA;kBACE+E,OAAO,EAAEf,aAAc;kBACvBG,SAAS,EAAC,8PAA8P;kBAAAC,QAAA,gBAExQpE,OAAA,CAACN,WAAW;oBAACyE,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnCxE,OAAA;oBAAAoE,QAAA,EAAM;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACTxE,OAAA;kBACE+E,OAAO,EAAEb,eAAgB;kBACzBC,SAAS,EAAC,kQAAkQ;kBAAAC,QAAA,gBAE5QpE,OAAA,CAACJ,WAAW;oBAACuE,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnCxE,OAAA;oBAAAoE,QAAA,EAAM;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA,eACT,CACH,EAEA/D,cAAc,KAAK,QAAQ,iBAC1BT,OAAA;gBAAKmE,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CpE,OAAA;kBACE+E,OAAO,EAAEhB,eAAgB;kBACzBiB,QAAQ,EAAEzE,SAAU;kBACpB4D,SAAS,EAAC,kTAAkT;kBAAAC,QAAA,gBAE5TpE,OAAA,CAACP,SAAS;oBAAC0E,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACjCxE,OAAA;oBAAAoE,QAAA,EAAM;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACTxE,OAAA;kBACE+E,OAAO,EAAEf,aAAc;kBACvBG,SAAS,EAAC,8NAA8N;kBAAAC,QAAA,EACzO;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGE,CAAC,eAGNxE,OAAA;cACE+E,OAAO,EAAEf,aAAc;cACvBG,SAAS,EAAC,gHAAgH;cAC1Hc,KAAK,EAAC,0BAAM;cAAAb,QAAA,eAEZpE,OAAA,CAACP,SAAS;gBAAC0E,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAIL/D,cAAc,KAAK,QAAQ,IAAII,UAAU,KAAK,IAAI,iBACjDb,OAAA;YAAKmE,SAAS,EAAC,kGAAkG;YAAAC,QAAA,eAC/GpE,OAAA;cAAKmE,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CpE,OAAA;gBAAKmE,SAAS,EAAC,+GAA+G;gBAAAC,QAAA,eAC5HpE,OAAA,CAACJ,WAAW;kBAACuE,SAAS,EAAC;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACNxE,OAAA;gBAAAoE,QAAA,gBACEpE,OAAA;kBAAKmE,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAC;gBAE3D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNxE,OAAA;kBAAKmE,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,GAAC,SAClC,EAACvD,UAAU,GAAG,CAAC,EAAC,6FACpB;gBAAA;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLzD,SAAS,iBACRf,OAAA;MAAKmE,SAAS,EAAC,2HAA2H;MAAAC,QAAA,gBACxIpE,OAAA,CAACN,WAAW;QAACyE,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnCxE,OAAA;QAAAoE,QAAA,EAAM;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACpE,EAAA,CAleID,gBAAgB;AAAA+E,EAAA,GAAhB/E,gBAAgB;AAoetB,eAAeA,gBAAgB;AAAC,IAAA+E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}