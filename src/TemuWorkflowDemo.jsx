import React, { useState } from 'react';
import { Play, RefreshCw, CheckCircle, XCircle, AlertCircle, Loader2, Save } from 'lucide-react';

const TemuWorkflowDemo = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [workflowStatus, setWorkflowStatus] = useState('idle'); // idle, running, completed, failed
  const [visibleSteps, setVisibleSteps] = useState(1); // 控制显示的步骤数量
  const [failedStep, setFailedStep] = useState(null); // 记录失败的步骤
  const [showToast, setShowToast] = useState(false); // 控制toast显示

  // 工作流步骤定义
  const workflowSteps = [
    {
      id: 'login',
      name: '登录',
      runningDescription: '正在验证登录信息...',
      completedDescription: '已完成登录【Temu账号】名下的店铺1',
      duration: 2000,
      status: 'pending'
    },
    {
      id: 'product_filter',
      name: '商品筛选', 
      runningDescription: '正在筛选符合条件的商品...',
      completedDescription: '已筛选出【Temu账号】名下的店铺1，普通流量加速特权≥$5的商品，共200件商品',
      duration: 3000,
      status: 'pending'
    },
    {
      id: 'product_processing',
      name: '商品加权',
      runningDescription: '正在为商品设置流量加权...',
      completedDescription: '已完成对筛选出的商品，设置30天的普通流量加速加权',
      duration: 4000,
      status: 'pending'
    },
    {
      id: 'result',
      name: '结果',
      runningDescription: '正在生成执行结果...',
      completedDescription: '执行完成！共处理200个商品，成功加速186个，跳过14个，总费用$892.40',
      duration: 1000,
      status: 'pending'
    }
  ];

  const [steps, setSteps] = useState(workflowSteps);

  // 获取步骤描述
  const getStepDescription = (step) => {
    if (step.status === 'running') {
      return step.runningDescription;
    } else if (step.status === 'completed') {
      return step.completedDescription;
    } else if (step.status === 'failed') {
      return `执行失败：${step.runningDescription}`;
    }
    return '等待执行...';
  };

  // 模拟工作流执行
  const executeWorkflow = async () => {
    setIsRunning(true);
    setWorkflowStatus('running');
    setFailedStep(null);

    // 从当前失败步骤或第一步开始
    const startStep = failedStep !== null ? failedStep : 0;
    setCurrentStep(startStep);

    // 如果是重新开始，重置步骤状态
    if (failedStep === null) {
      setVisibleSteps(1);
      setSteps(prev => prev.map(step => ({ ...step, status: 'pending' })));
    }

    for (let i = startStep; i < steps.length; i++) {
      setCurrentStep(i);

      // 显示当前步骤（如果还未显示）
      if (i + 1 > visibleSteps) {
        setVisibleSteps(i + 1);
        // 确保新显示的步骤处于pending状态
        setSteps(prev => prev.map((step, index) =>
          index === i ? { ...step, status: 'pending' } : step
        ));
        await new Promise(resolve => setTimeout(resolve, 300)); // 短暂延迟显示步骤
      }

      // 设置当前步骤为运行中
      setSteps(prev => prev.map((step, index) =>
        index === i ? { ...step, status: 'running' } : step
      ));

      // 等待步骤完成
      await new Promise(resolve => setTimeout(resolve, steps[i].duration));

      // 随机失败演示（15%概率，在第2或第3步）
      const shouldFail = Math.random() < 0.15 && (i === 1 || i === 2);

      if (shouldFail) {
        setSteps(prev => prev.map((step, index) =>
          index === i ? { ...step, status: 'failed' } : step
        ));
        setWorkflowStatus('failed');
        setFailedStep(i);
        setIsRunning(false);
        return;
      }

      // 设置步骤为完成
      setSteps(prev => prev.map((step, index) =>
        index === i ? { ...step, status: 'completed' } : step
      ));

      // 完成一步后显示下一步（如果不是最后一步）
      if (i < steps.length - 1) {
        setVisibleSteps(i + 2);
        // 确保下一步显示为pending状态
        setSteps(prev => prev.map((step, index) =>
          index === i + 1 ? { ...step, status: 'pending' } : step
        ));
        await new Promise(resolve => setTimeout(resolve, 500)); // 延迟显示下一步
      }
    }

    setWorkflowStatus('completed');
    setFailedStep(null);
    setIsRunning(false);
  };

  // 重试失败的步骤
  const retryFromFailed = () => {
    if (failedStep !== null) {
      // 重置失败步骤的状态
      setSteps(prev => prev.map((step, index) => 
        index === failedStep ? { ...step, status: 'pending' } : step
      ));
      executeWorkflow();
    }
  };

  // 重置整个工作流
  const resetWorkflow = () => {
    setIsRunning(false);
    setWorkflowStatus('idle');
    setCurrentStep(0);
    setVisibleSteps(1);
    setFailedStep(null);
    setSteps(workflowSteps.map(step => ({ ...step, status: 'pending' })));
  };

  // 保存计划
  const savePlan = () => {
    setShowToast(true);
    setTimeout(() => setShowToast(false), 3000);
  };

  return (
    <div className="w-full">
      {/* AI对话框容器 */}
      <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg overflow-hidden border border-white/20">
        {/* 对话框头部 */}
        <div className="bg-blue-500 text-white p-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <span className="text-sm font-bold">AI</span>
            </div>
            <div>
              <h3 className="font-semibold">Temu自动化助手</h3>
              <p className="text-blue-100 text-sm">流量加速自动化执行中...</p>
            </div>
          </div>
        </div>

        {/* 对话内容 */}
        <div className="p-6">
          {/* AI消息 */}
          <div className="mb-6">
            <div className="bg-gray-100 rounded-lg p-4 mb-4">
              <p className="text-gray-800 mb-2">好的，开始执行计划</p>
              <div className="text-sm text-gray-600 bg-white rounded-lg p-3 border">
                <span className="font-medium">执行配置：</span>高级流量加权档位，价格范围4-6美元，时效30天
              </div>
            </div>
          </div>

          {/* 优化后的时间线步骤列表 - 完美连接线 */}
          <div className="relative">
            {steps.slice(0, visibleSteps).map((step, index) => {
              const nextStep = steps[index + 1];
              const shouldShowConnector = index < visibleSteps - 1 && index < steps.length - 1;
              const connectorColor = step.status === 'completed' ? 'bg-green-500' : 'bg-gray-300';

              return (
                <div key={step.id} className="relative">
                  {/* 连接线 - 只在步骤完成后显示绿色，否则显示灰色 */}
                  {shouldShowConnector && (
                    <div
                      className={`absolute left-4 top-8 w-0.5 transition-all duration-500 z-0 ${connectorColor}`}
                      style={{
                        height: '3rem' // 固定高度，只连接到下一个步骤的起始位置
                      }}
                    ></div>
                  )}

                  {/* 步骤内容容器 */}
                  <div className="flex items-start space-x-4 relative z-10 pb-6">
                    {/* 状态图标容器 - 优化层级确保完美覆盖 */}
                    <div className="flex-shrink-0 relative">
                      {/* 图标背景圆圈 - 确保完全覆盖连接线 */}
                      <div className="w-8 h-8 bg-white rounded-full absolute inset-0 z-10 border border-gray-100"></div>

                      {/* 状态图标 - 最高层级 */}
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 relative z-20 shadow-sm ${
                        step.status === 'completed' ? 'bg-green-500 text-white' :
                        step.status === 'failed' ? 'bg-red-500 text-white' :
                        step.status === 'running' ? 'bg-blue-500 text-white' :
                        'bg-gray-300 text-gray-600'
                      }`}>
                        {step.status === 'completed' && <CheckCircle className="w-5 h-5" />}
                        {step.status === 'failed' && <XCircle className="w-5 h-5" />}
                        {step.status === 'running' && <Loader2 className="w-5 h-5 animate-spin" />}
                        {step.status === 'pending' && <div className="w-3 h-3 rounded-full bg-gray-600"></div>}
                      </div>
                    </div>

                  {/* 步骤内容 */}
                  <div className="flex-1 min-w-0 pt-1">
                    <div className="mb-2">
                      <h4 className="text-lg font-medium text-gray-900">{step.name}</h4>
                    </div>

                    <p className="text-sm text-gray-600 leading-relaxed">
                      {getStepDescription(step)}
                    </p>

                    {/* 失败时的状态消息容器 */}
                    {step.status === 'failed' && (
                      <div className="mt-2 bg-red-50 border border-red-200 rounded-lg p-3">
                        <div className="flex items-center justify-between">
                          <p className="text-red-700 text-sm">
                            执行失败，可能是网络连接问题或系统繁忙
                          </p>
                        </div>
                        <div className="mt-2">
                          <button
                            onClick={retryFromFailed}
                            className="text-red-700 text-sm font-medium hover:underline"
                          >
                            重试
                          </button>
                        </div>
                      </div>
                    )}

                    {/* 成功时的状态消息容器 */}
                    {step.status === 'completed' && workflowStatus === 'completed' && index === steps.length - 1 && (
                                <><button
                          onClick={savePlan}
                          className="ml-3 p-1.5 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-full transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 active:translate-y-0"
                          title="保存计划"
                        >
                          <Save className="w-3 h-3" />
                        </button>
                        <div className="mt-2 bg-green-50 border border-green-200 rounded-lg p-3">
                            <div className="flex items-center justify-between">
                              <p className="text-green-700 text-sm">
                                执行成功完成
                              </p>
                            </div>
                          </div></> 
                    )}
                  </div>
                </div>
              </div>
            );
            })}
          </div>

          {/* 操作按钮区域 */}
          <div className="mt-8 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {workflowStatus === 'idle' && (
                  <button
                    onClick={executeWorkflow}
                    disabled={isRunning}
                    className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2"
                  >
                    <Play className="w-4 h-4" />
                    <span>开始执行</span>
                  </button>
                )}

                {workflowStatus === 'running' && (
                  <button
                    disabled
                    className="bg-gradient-to-r from-gray-400 to-gray-500 text-white px-6 py-3 rounded-xl text-sm font-medium cursor-not-allowed flex items-center space-x-2"
                  >
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span>执行中...</span>
                  </button>
                )}

                {workflowStatus === 'completed' && (
                  <button
                    onClick={resetWorkflow}
                    className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2"
                  >
                    <CheckCircle className="w-4 h-4" />
                    <span>执行完成</span>
                  </button>
                )}

                {workflowStatus === 'failed' && (
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={retryFromFailed}
                      disabled={isRunning}
                      className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2"
                    >
                      <RefreshCw className="w-4 h-4" />
                      <span>重试</span>
                    </button>
                    <button
                      onClick={resetWorkflow}
                      className="bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0"
                    >
                      重新开始
                    </button>
                  </div>
                )}

                {/* 整体进度指示 */}
                {workflowStatus === 'running' && (
                  <div className="flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl px-4 py-3 border border-blue-200/50 shadow-sm">
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center">
                      <Loader2 className="w-4 h-4 text-white animate-spin" />
                    </div>
                    <span className="text-sm text-blue-700 font-medium">
                      正在执行第 {currentStep + 1} 步，共 {steps.length} 步
                    </span>
                  </div>
                )}
              </div>

              {/* 刷新按钮 */}
              <button
                onClick={resetWorkflow}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:shadow-sm"
                title="重新开始"
              >
                <RefreshCw className="w-4 h-4" />
              </button>
            </div>

            {/* 状态指示器 */}
            {workflowStatus === 'completed' && (
              <div className="mt-6 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200/50 rounded-xl p-6 shadow-sm">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-lg">
                    <CheckCircle className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <div className="text-green-700 font-semibold text-base mb-1">
                      🎉 自动化执行完成！
                    </div>
                    <div className="text-green-600 text-sm">
                      所有商品已成功配置流量加速，系统运行正常
                    </div>
                  </div>
                </div>
              </div>
            )}

            {workflowStatus === 'failed' && failedStep !== null && (
              <div className="mt-6 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200/50 rounded-xl p-6 shadow-sm">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg">
                    <AlertCircle className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <div className="text-red-700 font-semibold text-base mb-1">
                      执行中断，需要处理
                    </div>
                    <div className="text-red-600 text-sm">
                      第 {failedStep + 1} 步出现问题，可点击重试继续执行
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Toast 通知 */}
      {showToast && (
        <div className="fixed top-4 right-4 z-50 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg flex items-center space-x-2 animate-pulse">
          <CheckCircle className="w-5 h-5" />
          <span>保存计划成功</span>
        </div>
      )}
    </div>
  );
};

export default TemuWorkflowDemo;
