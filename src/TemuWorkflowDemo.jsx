import React, { useState, useEffect, useRef } from 'react';
import { Play, RefreshCw, CheckCircle, XCircle, AlertCircle, Loader2, Save } from 'lucide-react';

const TemuWorkflowDemo = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [workflowStatus, setWorkflowStatus] = useState('idle'); // idle, running, completed, failed
  const [visibleSteps, setVisibleSteps] = useState(0); // 控制显示的步骤数量
  const [failedStep, setFailedStep] = useState(null); // 记录失败的步骤
  const [showToast, setShowToast] = useState(false); // 控制toast显示
  const [toastMessage, setToastMessage] = useState('保存计划成功'); // toast消息内容
  const [productProcessingStep, setProductProcessingStep] = useState(0); // 商品加权进度步骤
  const [startTime, setStartTime] = useState(null); // 执行开始时间
  const [elapsedTime, setElapsedTime] = useState(0); // 累计执行时间（秒）
  const intervalRef = useRef(null); // 定时器引用
  const [isMultiStore, setIsMultiStore] = useState(false); // 是否为多店铺模式
  const [showExceptionDetails, setShowExceptionDetails] = useState(false); // 是否显示异常详情

  // 工作流步骤定义
  const workflowSteps = [
    {
      id: 'login',
      name: '登录',
      runningDescription: '正在验证登录信息...',
      completedDescription: '已完成登录【Temu账号】名下的店铺1',
      duration: 2000,
      status: 'pending'
    },
    {
      id: 'product_processing',
      name: '商品流量加速',
      runningDescription: '正在为商品流量加速...',
      completedDescription: '全部商品流量加速任务已完成！所有商品已按规则完成流量加速。',
      duration: 4000,
      status: 'pending',
      progressSteps: [
        '商品流量加速任务已启动：正在按照预设规则对所有商品进行处理，单个商品处理时间约20-30秒',
        '商品SPU ID: 204845849 已开启30天普通流量加速加权。开始处理下一个商品...'
      ]
    },
    {
      id: 'result',
      name: '结果',
      runningDescription: '正在生成执行结果...',
      completedDescription: '本次任务执行完毕。共成功处理48个商品。另有2个商品未能成功，原因如下：【商品名称A】因AI判断其价格为6.2，高于5的设定而被跳过；【商品名称B】因页面长时间无法打开而执行失败。',
      duration: 1000,
      status: 'pending'
    }
  ];

  // 多店铺工作流步骤定义
  const multiStoreWorkflowSteps = [
    {
      id: 'login_store1',
      name: '登录店铺1',
      runningDescription: '正在验证店铺1登录信息...',
      completedDescription: '已完成登录【Temu账号】名下的店铺1',
      duration: 2000,
      status: 'pending'
    },
    {
      id: 'traffic_acceleration_store1',
      name: '流量加速店铺1',
      runningDescription: '正在为店铺1设置流量加速...',
      completedDescription: '已完成店铺1的流量加速设置',
      duration: 3000,
      status: 'pending'
    },
    {
      id: 'login_store2',
      name: '登录店铺2',
      runningDescription: '正在验证店铺2登录信息...',
      completedDescription: '已完成登录【Temu账号】名下的店铺2',
      duration: 2000,
      status: 'pending'
    },
    {
      id: 'product_processing_store2',
      name: '商品加权店铺2',
      runningDescription: '正在为店铺2商品设置流量加权...',
      completedDescription: '已完成店铺2商品的流量加权设置',
      duration: 4000,
      status: 'pending',
      progressSteps: [
        '正在处理店铺2商品',
        '店铺2商品已开启30天的普通流量加速加权',
        '已完成店铺2商品流量加权设置'
      ]
    },
    {
      id: 'multi_store_result',
      name: '汇总结果',
      runningDescription: '正在生成多店铺执行结果...',
      completedDescription: '多店铺任务执行完毕',
      duration: 1000,
      status: 'pending'
    }
  ];

  const [steps, setSteps] = useState(workflowSteps);

  // 时间统计效果
  useEffect(() => {
    if (workflowStatus === 'running' && startTime) {
      const baseElapsedTime = elapsedTime; // 保存基础累计时间
      intervalRef.current = setInterval(() => {
        const now = Date.now();
        const currentSessionElapsed = Math.floor((now - startTime) / 1000);
        setElapsedTime(baseElapsedTime + currentSessionElapsed);
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [workflowStatus, startTime]);

  // 格式化时间显示
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 获取步骤描述
  const getStepDescription = (step) => {
    if (step.status === 'running') {
      // 商品加权步骤显示动态进度文案（支持单店铺和多店铺）
      if ((step.id === 'product_processing' || step.id === 'product_processing_store2') && step.progressSteps) {
        return step.progressSteps[productProcessingStep] || step.runningDescription;
      }
      return step.runningDescription;
    } else if (step.status === 'completed') {
      return step.completedDescription;
    } else if (step.status === 'failed') {
      return `执行失败：${step.runningDescription}`;
    }
    return '等待执行...';
  };

  // 模拟工作流执行
  const executeWorkflow = async () => {
    setIsRunning(true);
    setWorkflowStatus('running');
    setFailedStep(null);

    // 启动计时器（如果是重试，保持累计时间）
    const now = Date.now();
    setStartTime(now);

    // 从当前失败步骤或第一步开始
    const startStep = failedStep !== null ? failedStep : 0;
    setCurrentStep(startStep);

    // 如果是重新开始，重置步骤状态
    if (failedStep === null) {
      setVisibleSteps(0);
      setSteps(prev => prev.map(step => ({ ...step, status: 'pending' })));
    }

    for (let i = startStep; i < steps.length; i++) {
      setCurrentStep(i);

      // 显示当前步骤（如果还未显示）
      if (i + 1 > visibleSteps) {
        setVisibleSteps(i + 1);
        // 确保新显示的步骤处于pending状态
        setSteps(prev => prev.map((step, index) =>
          index === i ? { ...step, status: 'pending' } : step
        ));
        await new Promise(resolve => setTimeout(resolve, 300)); // 短暂延迟显示步骤
      }

      // 设置当前步骤为运行中
      setSteps(prev => prev.map((step, index) =>
        index === i ? { ...step, status: 'running' } : step
      ));

      // 商品加权步骤的动态文案切换（支持单店铺和多店铺）
      if ((steps[i].id === 'product_processing' || steps[i].id === 'product_processing_store2') && steps[i].progressSteps) {
        const progressSteps = steps[i].progressSteps;
        for (let j = 0; j < progressSteps.length; j++) {
          setProductProcessingStep(j);
          await new Promise(resolve => setTimeout(resolve, steps[i].duration / progressSteps.length));
        }
      } else {
        // 等待步骤完成
        await new Promise(resolve => setTimeout(resolve, steps[i].duration));
      }

      // 随机失败演示（15%概率，在第2或第3步）
      const shouldFail = Math.random() < 0.15 && (i === 1 || i === 2);

      if (shouldFail) {
        setSteps(prev => prev.map((step, index) =>
          index === i ? { ...step, status: 'failed' } : step
        ));
        setWorkflowStatus('failed');
        setFailedStep(i);
        setIsRunning(false);

        // 失败时保存当前累计时间
        if (startTime) {
          const now = Date.now();
          const currentElapsed = Math.floor((now - startTime) / 1000);
          setElapsedTime(prev => prev + currentElapsed);
          setStartTime(null);
        }
        return;
      }

      // 设置步骤为完成
      setSteps(prev => prev.map((step, index) =>
        index === i ? { ...step, status: 'completed' } : step
      ));

      // 完成一步后显示下一步（如果不是最后一步）
      if (i < steps.length - 1) {
        setVisibleSteps(i + 2);
        // 确保下一步显示为pending状态
        setSteps(prev => prev.map((step, index) =>
          index === i + 1 ? { ...step, status: 'pending' } : step
        ));
        await new Promise(resolve => setTimeout(resolve, 500)); // 延迟显示下一步
      }
    }

    setWorkflowStatus('completed');
    setFailedStep(null);
    setIsRunning(false);

    // 完成时保存最终累计时间
    if (startTime) {
      const now = Date.now();
      const currentElapsed = Math.floor((now - startTime) / 1000);
      setElapsedTime(prev => prev + currentElapsed);
      setStartTime(null);
    }
  };

  // 重试失败的步骤
  const retryFromFailed = () => {
    if (failedStep !== null) {
      // 重置失败步骤的状态
      setSteps(prev => prev.map((step, index) => 
        index === failedStep ? { ...step, status: 'pending' } : step
      ));
      executeWorkflow();
    }
  };

  // 重置整个工作流
  const resetWorkflow = () => {
    setIsRunning(false);
    setWorkflowStatus('idle');
    setCurrentStep(0);
    setVisibleSteps(0);
    setFailedStep(null);
    setProductProcessingStep(0);
    setStartTime(null);
    setElapsedTime(0); // 完全重置时清零时间
    const currentWorkflowSteps = isMultiStore ? multiStoreWorkflowSteps : workflowSteps;
    setSteps(currentWorkflowSteps.map(step => ({ ...step, status: 'pending' })));
  };

  // 保存计划
  const savePlan = () => {
    setToastMessage('保存计划成功');
    setShowToast(true);
    setTimeout(() => setShowToast(false), 3000);
  };

  // 下载日志
  const downloadLog = () => {
    setToastMessage('下载成功');
    setShowToast(true);
    setTimeout(() => setShowToast(false), 3000);
  };

  // 切换异常详情显示
  const toggleExceptionDetails = () => {
    setShowExceptionDetails(!showExceptionDetails);
  };

  // 切换到多店铺模式
  const switchToMultiStore = () => {
    setIsMultiStore(true);
    setSteps(multiStoreWorkflowSteps.map(step => ({ ...step, status: 'pending' })));
    setWorkflowStatus('idle');
    setVisibleSteps(0);
    setFailedStep(null);
    setProductProcessingStep(0);
    setStartTime(null);
    setElapsedTime(0); // 切换模式时重置时间
  };

  // 切换到单店铺模式
  const switchToSingleStore = () => {
    setIsMultiStore(false);
    setSteps(workflowSteps.map(step => ({ ...step, status: 'pending' })));
    setWorkflowStatus('idle');
    setVisibleSteps(0);
    setFailedStep(null);
    setProductProcessingStep(0);
    setStartTime(null);
    setElapsedTime(0); // 切换模式时重置时间
  };

  // 模拟失败状态
  const simulateFailure = () => {
    setVisibleSteps(3);
    setSteps(prev => prev.map((step, index) => {
      if (index === 0) return { ...step, status: 'completed' };
      if (index === 1) return { ...step, status: 'completed' };
      if (index === 2) return { ...step, status: 'failed' };
      return step;
    }));
    setWorkflowStatus('failed');
    setFailedStep(2);
  };

  return (
    <div className="w-full">
      {/* AI对话框容器 */}
      <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg overflow-hidden border border-white/20">
        {/* 对话框头部 */}
        <div className="bg-blue-500 text-white p-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <span className="text-sm font-bold">AI</span>
            </div>
            <div>
              <h3 className="font-semibold">Temu自动化助手</h3>
              <p className="text-blue-100 text-sm">流量加速自动化执行中...</p>
            </div>
          </div>
        </div>

        {/* 对话内容 */}
        <div className="p-6">
          {/* AI消息 */}
          <div className="mb-6">
            <div className="bg-gray-100 rounded-lg p-4 mb-4">
              <p className="text-gray-800 mb-2">好的，开始执行计划</p>
              <div className="text-sm text-gray-600 bg-white rounded-lg p-3 border">
                <span className="font-medium">执行配置：</span>普通流量加权档位，价格范围4-6美元，时效30天
              </div>
            </div>
          </div>

          {/* 执行开始提示区域 */}
          {(workflowStatus === 'running' || workflowStatus === 'completed' || workflowStatus === 'failed') && (
            <div className="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                    {workflowStatus === 'running' ? (
                      <Loader2 className="w-3 h-3 text-white animate-spin" />
                    ) : (
                      <CheckCircle className="w-3 h-3 text-white" />
                    )}
                  </div>
                  <span className="text-blue-800 font-medium">好的，开始执行计划</span>
                </div>
                <div className="text-sm text-blue-600">
                  <span className="font-medium">执行时间：</span>
                  <span className="font-mono">{formatTime(elapsedTime)}</span>
                </div>
              </div>
            </div>
          )}

          {/* 优化后的时间线步骤列表 - 完美连接线 */}
          <div className="relative">
            {steps.slice(0, visibleSteps).map((step, index) => {
              const nextStep = steps[index + 1];
              const shouldShowConnector = index < visibleSteps - 1 && index < steps.length - 1;
              const connectorColor = step.status === 'completed' ? 'bg-green-500' : 'bg-gray-300';

              return (
                <div key={step.id} className="relative">
                  {/* 连接线 - 只在步骤完成后显示绿色，否则显示灰色 */}
                  {shouldShowConnector && (
                    <div
                      className={`absolute left-4 top-8 w-0.5 transition-all duration-500 z-0 ${connectorColor}`}
                      style={{
                        height: '3rem' // 固定高度，只连接到下一个步骤的起始位置
                      }}
                    ></div>
                  )}

                  {/* 步骤内容容器 */}
                  <div className="flex items-start space-x-4 relative z-10 pb-6">
                    {/* 状态图标容器 - 优化层级确保完美覆盖 */}
                    <div className="flex-shrink-0 relative">
                      {/* 图标背景圆圈 - 确保完全覆盖连接线 */}
                      <div className="w-8 h-8 bg-white rounded-full absolute inset-0 z-10 border border-gray-100"></div>

                      {/* 状态图标 - 最高层级 */}
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 relative z-20 shadow-sm ${
                        step.status === 'completed' ? 'bg-green-500 text-white' :
                        step.status === 'failed' ? 'bg-red-500 text-white' :
                        step.status === 'running' ? 'bg-blue-500 text-white' :
                        step.status === 'pending' ? 'bg-blue-500 text-white' :
                        'bg-gray-300 text-gray-600'
                      }`}>
                        {step.status === 'completed' && <CheckCircle className="w-5 h-5" />}
                        {step.status === 'failed' && <XCircle className="w-5 h-5" />}
                        {step.status === 'running' && <Loader2 className="w-5 h-5 animate-spin" />}
                        {step.status === 'pending' && <Loader2 className="w-5 h-5 animate-spin" />}
                      </div>
                    </div>

                  {/* 步骤内容 */}
                  <div className="flex-1 min-w-0 pt-1">
                    <div className="mb-2">
                      <h4 className="text-lg font-medium text-gray-900">{step.name}</h4>
                    </div>

                    {/* 只在非失败状态时显示描述 */}
                    {step.status !== 'failed' && (
                      <div className="text-sm text-gray-600 leading-relaxed">
                        {(step.id === 'result' || step.id === 'multi_store_result') && step.status === 'completed' ? (
                          <div>
                            {step.id === 'multi_store_result' ? (
                              // 多店铺汇总结果
                              <div>
                                <div className="font-medium mb-3">✅ 本次任务执行完毕</div>

                                <div className="mb-3">
                                  <div className="font-medium mb-2">📊 处理汇总：</div>
                                  <div className="space-y-1 pl-4">
                                    <div>- 店铺1：成功处理 <span className="font-semibold text-green-600">48个商品</span></div>
                                    <div>- 店铺2：成功处理 <span className="font-semibold text-green-600">30个商品</span></div>
                                    <div>- 总计：<span className="font-semibold text-blue-600">78个商品</span>处理完成</div>
                                  </div>
                                </div>

                                <div className="mb-3">
                                  <div className="font-medium mb-2">⚠️ 异常商品详情：</div>
                                  <div className="space-y-1 pl-4">
                                    <div>【店铺1】异常统计：不符合条件(2个)│处理失败(2个)</div>
                                    <div>【店铺2】异常统计：不符合条件(2个)│处理失败(2个)</div>
                                  </div>

                                  <div className="mt-3 flex items-center space-x-3">
                                    <button
                                      onClick={toggleExceptionDetails}
                                      className="px-3 py-1 bg-orange-100 text-orange-700 rounded-lg text-sm hover:bg-orange-200 transition-colors"
                                    >
                                      {showExceptionDetails ? '隐藏详情' : '查看详情'}
                                    </button>
                                    <button
                                      onClick={downloadLog}
                                      className="px-3 py-1 bg-blue-100 text-blue-700 rounded-lg text-sm hover:bg-blue-200 transition-colors"
                                    >
                                      下载日志
                                    </button>
                                  </div>

                                  {showExceptionDetails && (
                                    <div className="mt-3 p-3 bg-gray-50 rounded-lg text-sm">
                                      <div className="mb-3">
                                        <div className="font-medium text-orange-700 mb-1">【店铺1】详细异常：</div>
                                        <div className="pl-4 space-y-1">
                                          <div>├─ 不符合条件(2个)：</div>
                                          <div className="pl-4">
                                            <div>│  • SPU ID: 204845842</div>
                                            <div>│  • SPU ID: 204845843</div>
                                          </div>
                                          <div>└─ 处理失败(2个)：</div>
                                          <div className="pl-4">
                                            <div>   • SPU ID: 204845849</div>
                                            <div>   • SPU ID: 204845844</div>
                                          </div>
                                        </div>
                                      </div>

                                      <div>
                                        <div className="font-medium text-orange-700 mb-1">【店铺2】详细异常：</div>
                                        <div className="pl-4 space-y-1">
                                          <div>├─ 不符合条件(2个)：</div>
                                          <div className="pl-4">
                                            <div>│  • SPU ID: 204845842</div>
                                            <div>│  • SPU ID: 204845843</div>
                                          </div>
                                          <div>└─ 处理失败(2个)：</div>
                                          <div className="pl-4">
                                            <div>   • SPU ID: 204845849</div>
                                            <div>   • SPU ID: 204845844</div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                            ) : (
                              // 单店铺结果
                              <div>
                                <div className="font-medium mb-3">✅ 本次任务执行完毕</div>

                                <div className="mb-3">
                                  <div className="font-medium mb-2">📊 处理汇总：</div>
                                  <div className="space-y-1 pl-4">
                                    <div>- 店铺1：成功处理 <span className="font-semibold text-green-600">48个商品</span></div>
                                  </div>
                                </div>

                                <div className="mb-3">
                                  <div className="font-medium mb-2">⚠️ 异常商品详情：</div>
                                  <div className="space-y-1 pl-4">
                                    <div>【店铺1】异常统计：不符合条件(2个)│处理失败(2个)</div>
                                  </div>

                                  <div className="mt-3 flex items-center space-x-3">
                                    <button
                                      onClick={toggleExceptionDetails}
                                      className="px-3 py-1 bg-orange-100 text-orange-700 rounded-lg text-sm hover:bg-orange-200 transition-colors"
                                    >
                                      {showExceptionDetails ? '隐藏详情' : '查看详情'}
                                    </button>
                                    <button
                                      onClick={downloadLog}
                                      className="px-3 py-1 bg-blue-100 text-blue-700 rounded-lg text-sm hover:bg-blue-200 transition-colors"
                                    >
                                      下载日志
                                    </button>
                                  </div>

                                  {showExceptionDetails && (
                                    <div className="mt-3 p-3 bg-gray-50 rounded-lg text-sm">
                                      <div className="font-medium text-orange-700 mb-1">【店铺1】详细异常：</div>
                                      <div className="pl-4 space-y-1">
                                        <div>├─ 不符合条件(2个)：</div>
                                        <div className="pl-4">
                                          <div>│  • SPU ID: 204845842</div>
                                          <div>│  • SPU ID: 204845843</div>
                                        </div>
                                        <div>└─ 处理失败(2个)：</div>
                                        <div className="pl-4">
                                          <div>   • SPU ID: 204845849</div>
                                          <div>   • SPU ID: 204845844</div>
                                        </div>
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        ) : (
                          <p>{getStepDescription(step)}</p>
                        )}
                      </div>
                    )}

                    {/* 失败时的状态消息容器 */}
                    {step.status === 'failed' && (
                        <p className="text-red-600 text-sm">
                          {getStepDescription(step)}
                        </p>
                    )}

                    {/* 重试按钮 - 在失败状态消息容器下方，与步骤标题左对齐 */}
                    {step.status === 'failed' && (
                      <div className="mt-3">
                        <button
                          onClick={retryFromFailed}
                          disabled={isRunning}
                          className="p-1.5 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-full transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 active:translate-y-0"
                          title="重试"
                        >
                          <RefreshCw className="w-3 h-3" />
                        </button>
                      </div>
                    )}

                    {/* 成功状态：椭圆形消息容器和保存按钮布局 */}
                    {step.status === 'completed' && workflowStatus === 'completed' && index === steps.length - 1 && (
                      <div className="mt-3 -ml-12 flex items-center space-x-2">
                        {/* 成功状态消息容器 - 椭圆形态，宽度跟随文字，左对齐到绿色图标 */}
                        <div className="inline-block bg-green-50 border border-green-200 rounded-full px-4 py-2">
                          <p className="text-green-700 text-sm whitespace-nowrap">
                           已完成当前所有计划
                          </p>
                        </div>

                        {/* 保存按钮 - 纯图标，在椭圆形容器右侧 */}
                        <button
                          onClick={savePlan}
                          className="p-1 text-gray-600 hover:text-blue-600 transition-all duration-200 hover:scale-110"
                          title="保存计划"
                        >
                          <Save className="w-4 h-4" />
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
            })}
          </div>

          {/* 操作按钮区域 */}
          <div className="mt-8 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {workflowStatus === 'idle' && (
                  <>
                    <button
                      onClick={executeWorkflow}
                      disabled={isRunning}
                      className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2"
                    >
                      <Play className="w-4 h-4" />
                      <span>{isMultiStore ? '开始多店铺执行' : '开始执行'}</span>
                    </button>
                    {!isMultiStore && (
                      <button
                        onClick={switchToMultiStore}
                        className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2"
                      >
                        <span>多店铺演示</span>
                      </button>
                    )}
                    {isMultiStore && (
                      <button
                        onClick={switchToSingleStore}
                        className="bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2"
                      >
                        <span>单店铺模式</span>
                      </button>
                    )}
                    <button
                      onClick={simulateFailure}
                      className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2"
                    >
                      <AlertCircle className="w-4 h-4" />
                      <span>模拟失败状态</span>
                    </button>
                  </>
                )}

                {workflowStatus === 'running' && (
                  <button
                    disabled
                    className="bg-gradient-to-r from-gray-400 to-gray-500 text-white px-6 py-3 rounded-xl text-sm font-medium cursor-not-allowed flex items-center space-x-2"
                  >
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span>执行中...</span>
                  </button>
                )}

                {workflowStatus === 'completed' && (
                  <>
                    <button
                      onClick={resetWorkflow}
                      className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2"
                    >
                      <CheckCircle className="w-4 h-4" />
                      <span>执行完成</span>
                    </button>
                    <button
                      onClick={simulateFailure}
                      className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2"
                    >
                      <AlertCircle className="w-4 h-4" />
                      <span>模拟失败状态</span>
                    </button>
                  </>
                )}

                {workflowStatus === 'failed' && (
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={retryFromFailed}
                      disabled={isRunning}
                      className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2"
                    >
                      <RefreshCw className="w-4 h-4" />
                      <span>重试</span>
                    </button>
                    <button
                      onClick={resetWorkflow}
                      className="bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0"
                    >
                      重新开始
                    </button>
                  </div>
                )}


              </div>

              {/* 刷新按钮 */}
              <button
                onClick={resetWorkflow}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:shadow-sm"
                title="重新开始"
              >
                <RefreshCw className="w-4 h-4" />
              </button>
            </div>

   

            {workflowStatus === 'failed' && failedStep !== null && (
              <div className="mt-6 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200/50 rounded-xl p-6 shadow-sm">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg">
                    <AlertCircle className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <div className="text-red-700 font-semibold text-base mb-1">
                      执行中断，需要处理
                    </div>
                    <div className="text-red-600 text-sm">
                      第 {failedStep + 1} 步出现问题，可点击重试继续执行
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Toast 通知 */}
      {showToast && (
        <div className="fixed top-4 right-4 z-50 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg flex items-center space-x-2 animate-pulse">
          <CheckCircle className="w-5 h-5" />
          <span>{toastMessage}</span>
        </div>
      )}
    </div>
  );
};

export default TemuWorkflowDemo;
